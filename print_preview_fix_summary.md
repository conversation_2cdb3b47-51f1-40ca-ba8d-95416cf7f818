# 打印预览页面问题修复总结

## 问题描述
小程序打印预览页面出现以下错误：
```
Setting data field "taskInfo" to undefined is invalid.
标签数据为空，无法绘制
```

## 问题分析

### 1. 数据传递问题
从错误日志可以看到：
- 数据被正确解析：`{bottleGroup: {…}}`
- 但是 `taskInfo` 为 `undefined`

### 2. 根本原因
在 `task-detail.js` 的 `handleBottlePrint` 方法中：
```javascript
// 错误的代码
const taskInfo = this.data.taskData  // taskData 不存在！

// 正确的代码应该是
const taskInfo = this.data.task      // 任务数据存储在 task 字段中
```

### 3. 数据流程
```
任务详情页面加载 → loadTaskDetail() → 设置 this.data.task
点击打印按钮 → handleBottlePrint() → 错误地使用 this.data.taskData
传递到预览页面 → taskInfo 为 undefined → 无法生成标签
```

## 修复内容

### 1. 修复任务详情页面数据引用 (miniprogram-native/pages/sampling/task-detail.js)
```javascript
// 修改前
const taskInfo = this.data.taskData

// 修改后
const taskInfo = this.data.task  // 修复：使用正确的字段名
```

### 2. 改进打印预览页面数据处理 (miniprogram-native/pages/printer/preview.js)
```javascript
// 修改前
if (!taskInfo || !bottleGroup) return

// 修改后
if (!bottleGroup) {
  console.error('缺少瓶组数据，无法生成标签')
  return
}

// 如果没有taskInfo，使用默认值或从bottleGroup中获取
const safeTaskInfo = taskInfo || {}
```

### 3. 增强数据获取逻辑
```javascript
// 从多个来源获取数据，提高容错性
const taskCode = safeTaskInfo.taskCode || safeTaskInfo.projectName || bottleGroup.taskCode || 'LIMS001'
const pointName = safeTaskInfo.pointName || safeTaskInfo.location || bottleGroup.pointName || '采样点位'
```

### 4. 添加调试日志
```javascript
console.log('打印数据准备:', { taskInfo, bottleGroup })
console.log('generateLabelData - taskInfo:', taskInfo)
console.log('generateLabelData - bottleGroup:', bottleGroup)
console.log('生成的标签数据:', labelData)
```

## 修复后的数据流程

```
任务详情页面 → 正确获取 this.data.task
↓
传递完整的 taskInfo 和 bottleGroup
↓
预览页面正确解析数据
↓
生成完整的标签数据
↓
成功绘制预览
```

## 验证步骤

1. **任务详情页面**：
   - 确保任务数据正确加载到 `this.data.task`
   - 点击瓶组打印按钮
   - 检查控制台日志：`打印数据准备: { taskInfo, bottleGroup }`

2. **打印预览页面**：
   - 检查数据解析：`解析的数据: {bottleGroup: {…}, taskInfo: {…}}`
   - 检查标签生成：`生成的标签数据: {...}`
   - 验证Canvas绘制成功

3. **预期结果**：
   - 不再出现 `taskInfo` 为 `undefined` 的错误
   - 标签数据正确生成
   - Canvas预览正常显示
   - 打印功能正常工作

## 相关文件

- `miniprogram-native/pages/sampling/task-detail.js` - 修复数据引用
- `miniprogram-native/pages/printer/preview.js` - 改进数据处理
- `print_preview_fix_summary.md` - 本修复说明

## 注意事项

1. **数据一致性**：确保所有引用任务数据的地方都使用正确的字段名
2. **容错处理**：即使部分数据缺失，也要能生成基本的标签
3. **调试信息**：保留必要的日志输出，便于后续问题排查
4. **向后兼容**：修复不应影响其他功能的正常使用
