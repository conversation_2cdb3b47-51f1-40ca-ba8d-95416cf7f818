# CPCL打印调试指南

## 🔍 当前问题

### 问题现象
- ❌ **中文显示为竖条**: 编码问题导致中文字符无法正确显示
- ❌ **字体太大**: DPI和字体设置导致文字过大

### 可能原因
1. **编码不匹配**: 打印机不支持UTF-8，需要GB2312/GBK
2. **DPI设置错误**: 203DPI可能过高，需要调整
3. **字体ID错误**: 字体选择不适合当前打印机
4. **CPCL指令格式**: 指令格式可能不符合打印机要求

## 🛠️ 调试步骤

### 第1步：基础连接测试
1. 连接打印机
2. 选择"基础CPCL测试"
3. 检查是否能打印纯英文内容

**预期结果**：
- ✅ 打印出"Basic Test"、"CPCL Printer"、"Font Size Test"
- ✅ 字体大小合适
- ✅ 布局整齐

### 第2步：完整功能测试
1. 选择"完整CPCL测试"
2. 检查英文内容和布局

**预期结果**：
- ✅ 设备信息正常显示
- ✅ 时间信息正确
- ✅ 纸张尺寸信息显示

### 第3步：中文编码测试
1. 选择"中文编码测试"
2. 观察不同编码方式的效果

**测试内容**：
- **UTF8**: 直接UTF-8编码
- **GB2312**: 声明GB2312编码
- **ASCII**: 纯英文对照
- **HEX**: 显示中文的十六进制编码

## 📋 调试指令对比

### 基础CPCL测试指令
```
! 0 200 200 200 1
TEXT 1 0 10 10 Basic Test
TEXT 1 0 10 30 CPCL Printer
TEXT 1 0 10 50 Font Size Test
PRINT
```

### 中文编码测试指令
```
! 0 200 200 300 1
TEXT 1 0 10 10 UTF8: [UTF-8字节]
ENCODING GB2312
TEXT 1 0 10 40 GB2312: [UTF-8字节]
TEXT 1 0 10 70 ASCII: Test OK
TEXT 1 0 10 100 HEX: E6 B5 8B E8 AF 95
PRINT
```

## 🔧 问题排查

### 1. 如果基础测试失败
**可能原因**：
- 打印机不支持CPCL指令集
- 连接问题
- 指令格式错误

**解决方案**：
- 确认打印机品牌和型号
- 检查是否为TSC、Zebra等CPCL兼容打印机
- 尝试重新连接

### 2. 如果英文正常但中文异常
**可能原因**：
- 中文编码不支持
- 字体不支持中文
- 编码声明错误

**解决方案**：
- 查看中文编码测试结果
- 尝试不同的编码方式
- 考虑使用纯英文标签

### 3. 如果字体太大
**可能原因**：
- DPI设置过高
- 字体ID选择错误
- SETMAG放大倍数过大

**解决方案**：
- 降低DPI到200或更低
- 使用字体ID 1（最小字体）
- 移除SETMAG指令

### 4. 如果布局混乱
**可能原因**：
- 坐标计算错误
- 标签尺寸设置不当
- 行间距过小

**解决方案**：
- 调整坐标倍数
- 检查纸张尺寸设置
- 增加行间距

## 💡 优化建议

### 1. 字体大小优化
```javascript
// 当前设置
TEXT 1 0 10 10 content  // 字体ID 1 = 最小字体

// 如果还是太大，尝试
TEXT 0 0 10 10 content  // 字体ID 0 = 更小字体
```

### 2. DPI调整
```javascript
// 当前设置
! 0 200 200 height 1

// 如果字体太大，尝试
! 0 150 150 height 1  // 降低DPI
```

### 3. 中文处理方案
```javascript
// 方案1: 纯英文替换
'样品类别' → 'Type'
'样品编号' → 'No'
'采样日期' → 'Date'

// 方案2: 拼音替换
'样品类别' → 'YangPin LeiBie'

// 方案3: 编码测试
查看HEX输出，确定正确的编码方式
```

## 🎯 测试流程

### 完整测试流程
1. **连接打印机** → 确保BLE连接正常
2. **基础测试** → 验证CPCL指令支持
3. **字体测试** → 确认字体大小合适
4. **编码测试** → 找到正确的中文编码方式
5. **布局测试** → 调整坐标和间距
6. **实际应用** → 在样品标签中测试

### 调试日志查看
在微信开发者工具控制台查看：
```javascript
// 指令生成日志
生成CPCL指令
生成基础CPCL测试指令
生成中文编码测试指令

// 数据发送日志
开始发送数据，总长度: XX 字节
发送第X块数据: [十六进制数据]
数据发送完成
```

## 📞 进一步调试

### 如果所有测试都失败
1. **确认打印机型号**：
   - 查看打印机标签或说明书
   - 确认是否支持CPCL指令集
   - 联系厂商获取指令手册

2. **尝试其他指令集**：
   - 某些打印机可能需要ESC/POS指令
   - 尝试ZPL指令（Zebra打印机）
   - 查看打印机支持的指令格式

3. **使用厂商软件测试**：
   - 下载打印机厂商提供的测试软件
   - 对比厂商软件发送的指令
   - 分析正确的指令格式

### 成功案例记录
如果找到有效的设置，请记录：
- 打印机品牌和型号
- 有效的DPI设置
- 正确的字体ID
- 中文编码方式
- 完整的测试指令

---

**通过系统的调试测试，应该能找到适合您打印机的正确设置！** 🎯

请按顺序进行测试，并观察每个测试的结果，这样可以逐步定位问题所在。
