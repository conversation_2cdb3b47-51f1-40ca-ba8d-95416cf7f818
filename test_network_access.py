#!/usr/bin/env python3
"""
测试网络访问脚本
用于验证后端服务在不同IP地址上的可访问性
"""

import requests
import json
import time

def test_api_access(base_url, endpoint="/sampling/task/my-tasks"):
    """测试API访问"""
    url = f"{base_url}{endpoint}"
    headers = {
        "Authorization": "Bearer test_token",
        "Content-Type": "application/json"
    }
    
    try:
        print(f"测试访问: {url}")
        response = requests.get(url, headers=headers, timeout=5)
        
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
                return True
            except json.JSONDecodeError:
                print(f"响应内容: {response.text}")
                return False
        else:
            print(f"错误响应: {response.text}")
            return False
            
    except requests.exceptions.ConnectTimeout:
        print("❌ 连接超时")
        return False
    except requests.exceptions.ConnectionError as e:
        print(f"❌ 连接错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

def main():
    """主函数"""
    print("=== LIMS后端服务网络访问测试 ===\n")
    
    # 测试不同的访问地址
    test_urls = [
        "http://127.0.0.1:9099",      # 本地访问
        "http://localhost:9099",       # 本地访问
        "http://************:9099",   # 局域网访问
        "http://0.0.0.0:9099",        # 通配符访问（通常不工作）
    ]
    
    results = {}
    
    for base_url in test_urls:
        print(f"\n--- 测试 {base_url} ---")
        success = test_api_access(base_url)
        results[base_url] = success
        print(f"结果: {'✅ 成功' if success else '❌ 失败'}")
        time.sleep(1)  # 避免请求过快
    
    print("\n=== 测试结果汇总 ===")
    for url, success in results.items():
        status = "✅ 可访问" if success else "❌ 不可访问"
        print(f"{url}: {status}")
    
    # 建议
    print("\n=== 建议 ===")
    if results.get("http://************:9099"):
        print("✅ 局域网访问正常，小程序可以使用 ************:9099")
    elif results.get("http://127.0.0.1:9099"):
        print("⚠️  只能本地访问，需要检查：")
        print("   1. 防火墙设置是否阻止了9099端口")
        print("   2. 后端服务是否绑定到0.0.0.0而不是127.0.0.1")
        print("   3. 网络配置是否正确")
    else:
        print("❌ 所有访问都失败，请检查后端服务是否正常运行")

if __name__ == "__main__":
    main()
