# API连接问题解决指南

## 🐛 问题描述

小程序尝试调用后端API时出现404错误：
```
INFO: 127.0.0.1:56474 - "POST /dev-api/api/auth/login HTTP/1.1" 404 Not Found
```

## ✅ 已修复的问题

### 1. API路径错误
- **原问题**: 小程序请求 `/api/auth/login`
- **实际路径**: 后端API路径是 `/login`
- **✅ 已修复**: 更新为正确的 `/login` 路径

### 2. 请求格式错误
- **原问题**: 发送JSON格式数据
- **后端要求**: `application/x-www-form-urlencoded` 格式
- **✅ 已修复**: 更新为表单格式数据

### 3. 微信开发者工具代理
- **问题**: 工具可能自动添加 `/dev-api` 前缀
- **✅ 已修复**: 使用正确的API路径

## 🔧 修复内容

### 1. 登录API修复
```javascript
// 修复前
const result = await app.request({
  url: '/api/auth/login',
  method: 'POST',
  data: {
    username: username.trim(),
    password: password.trim()
  }
})

// 修复后
const result = await app.request({
  url: '/login',
  method: 'POST',
  data: `username=${encodeURIComponent(username.trim())}&password=${encodeURIComponent(password.trim())}`,
  header: {
    'Content-Type': 'application/x-www-form-urlencoded'
  }
})
```

### 2. 请求方法增强
```javascript
// app.js 中的请求方法支持不同Content-Type
header: {
  'Content-Type': header['Content-Type'] || 'application/json',
  'Authorization': this.globalData.token ? `Bearer ${this.globalData.token}` : '',
  ...header
}
```

## 🧪 测试工具

我已经创建了一个API测试页面：`pages/debug/test`

### 功能特性
1. **连接测试** - 测试后端服务是否可达
2. **登录API测试** - 测试登录接口是否正常
3. **实时结果显示** - 显示详细的请求和响应信息

### 使用方法
1. 在小程序中导航到"系统测试"页面
2. 点击"测试连接"检查后端连接
3. 输入用户名密码，点击"测试登录"
4. 查看详细的测试结果

## 🚀 现在应该可以正常工作

### 预期结果
- ✅ 登录API返回200状态码
- ✅ 获取到用户信息和token
- ✅ 成功跳转到首页

### 如果仍有问题

1. **检查后端服务**
   ```bash
   # 确保后端服务在运行
   cd back
   python run.py
   ```

2. **检查端口配置**
   - 后端默认端口: 9099
   - 小程序配置: `app.js` 中的 `baseURL`

3. **检查网络权限**
   - 微信开发者工具 -> 详情 -> 本地设置
   - 勾选"不校验合法域名、web-view（业务域名）、TLS 版本以及 HTTPS 证书"

4. **使用测试页面诊断**
   - 导航到"系统测试"页面
   - 查看详细的错误信息
   - 根据错误信息进行调试

## 📞 需要帮助？

如果问题仍然存在，请提供：
1. 测试页面显示的错误信息
2. 后端服务的运行状态
3. 微信开发者工具的控制台输出

这样我可以提供更精确的解决方案。
