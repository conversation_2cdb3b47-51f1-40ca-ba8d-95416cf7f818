// 简化版text-encoding库，适配微信小程序环境
// 支持UTF-8和GBK编码

(function(global) {
  'use strict';

  // 字符串转UTF-8字节数组
  function stringToUTF8Bytes(str) {
    const bytes = [];
    for (let i = 0; i < str.length; i++) {
      const code = str.charCodeAt(i);
      if (code < 0x80) {
        bytes.push(code);
      } else if (code < 0x800) {
        bytes.push(0xC0 | (code >> 6));
        bytes.push(0x80 | (code & 0x3F));
      } else if (code < 0xD800 || code >= 0xE000) {
        bytes.push(0xE0 | (code >> 12));
        bytes.push(0x80 | ((code >> 6) & 0x3F));
        bytes.push(0x80 | (code & 0x3F));
      } else {
        // 代理对处理
        i++;
        const hi = code;
        const lo = str.charCodeAt(i);
        const codePoint = 0x10000 + (((hi & 0x3FF) << 10) | (lo & 0x3FF));
        bytes.push(0xF0 | (codePoint >> 18));
        bytes.push(0x80 | ((codePoint >> 12) & 0x3F));
        bytes.push(0x80 | ((codePoint >> 6) & 0x3F));
        bytes.push(0x80 | (codePoint & 0x3F));
      }
    }
    return bytes;
  }

  // UTF-8字节数组转字符串
  function utf8BytesToString(bytes) {
    let str = '';
    let i = 0;
    while (i < bytes.length) {
      const byte1 = bytes[i++];
      if (byte1 < 0x80) {
        str += String.fromCharCode(byte1);
      } else if ((byte1 >> 5) === 0x06) {
        const byte2 = bytes[i++];
        str += String.fromCharCode(((byte1 & 0x1F) << 6) | (byte2 & 0x3F));
      } else if ((byte1 >> 4) === 0x0E) {
        const byte2 = bytes[i++];
        const byte3 = bytes[i++];
        str += String.fromCharCode(((byte1 & 0x0F) << 12) | ((byte2 & 0x3F) << 6) | (byte3 & 0x3F));
      } else if ((byte1 >> 3) === 0x1E) {
        const byte2 = bytes[i++];
        const byte3 = bytes[i++];
        const byte4 = bytes[i++];
        const codePoint = ((byte1 & 0x07) << 18) | ((byte2 & 0x3F) << 12) | ((byte3 & 0x3F) << 6) | (byte4 & 0x3F);
        const hi = Math.floor((codePoint - 0x10000) / 0x400) + 0xD800;
        const lo = (codePoint - 0x10000) % 0x400 + 0xDC00;
        str += String.fromCharCode(hi, lo);
      }
    }
    return str;
  }

  // 常用中文字符GBK编码映射
  const chineseGBKMap = {
    '测': [0xB2, 0xE2], '试': [0xCA, 0xD4], '打': [0xB4, 0xF2], '印': [0xD3, 0xA1],
    '样': [0xD1, 0xF9], '品': [0xC6, 0xB7], '类': [0xC0, 0xE0], '别': [0xB1, 0xF0],
    '编': [0xB1, 0xE0], '号': [0xBA, 0xC5], '采': [0xB2, 0xC9], '日': [0xC8, 0xD5],
    '期': [0xC6, 0xDA], '时': [0xCA, 0xB1], '间': [0xBC, 0xE4], '点': [0xB5, 0xE3],
    '位': [0xCE, 0xBB], '检': [0xBC, 0xEC], '项': [0xCF, 0xEE], '目': [0xC4, 0xBF],
    '保': [0xB1, 0xA3], '存': [0xB4, 0xE6], '容': [0xC8, 0xDD], '器': [0xC6, 0xF7],
    '方': [0xB7, 0xBD], '式': [0xCA, 0xBD], '状': [0xD7, 0xB4], '态': [0xCC, 0xAC],
    '系': [0xCF, 0xB5], '统': [0xCD, 0xB3], '成': [0xB3, 0xC9], '功': [0xB9, 0xA6],
    '水': [0xCB, 0xAE], '温': [0xCE, 0xC2], '度': [0xB6, 0xC8], '值': [0xD6, 0xB5],
    '量': [0xC1, 0xBF], '质': [0xD6, 0xCA], '浓': [0xC5, 0xA8], '密': [0xC3, 0xDC]
  };

  // 字符串转GBK字节数组
  function stringToGBKBytes(str) {
    const bytes = [];
    for (let i = 0; i < str.length; i++) {
      const char = str.charAt(i);
      const code = str.charCodeAt(i);
      
      if (code < 0x80) {
        // ASCII字符
        bytes.push(code);
      } else if (chineseGBKMap[char]) {
        // 常用中文字符
        bytes.push(...chineseGBKMap[char]);
      } else {
        // 其他字符使用问号替代
        bytes.push(0x3F); // '?'
      }
    }
    return bytes;
  }

  // TextEncoder类
  function TextEncoder(encoding) {
    this.encoding = (encoding || 'utf-8').toLowerCase();
  }

  TextEncoder.prototype.encode = function(str) {
    if (this.encoding === 'utf-8' || this.encoding === 'utf8') {
      return new Uint8Array(stringToUTF8Bytes(str));
    } else if (this.encoding === 'gbk' || this.encoding === 'gb2312') {
      return new Uint8Array(stringToGBKBytes(str));
    } else {
      throw new Error('Unsupported encoding: ' + this.encoding);
    }
  };

  // TextDecoder类
  function TextDecoder(encoding) {
    this.encoding = (encoding || 'utf-8').toLowerCase();
  }

  TextDecoder.prototype.decode = function(bytes) {
    const byteArray = bytes instanceof Uint8Array ? Array.from(bytes) : bytes;
    
    if (this.encoding === 'utf-8' || this.encoding === 'utf8') {
      return utf8BytesToString(byteArray);
    } else if (this.encoding === 'gbk' || this.encoding === 'gb2312') {
      // GBK解码（简化实现）
      let str = '';
      for (let i = 0; i < byteArray.length; i++) {
        const byte = byteArray[i];
        if (byte < 0x80) {
          str += String.fromCharCode(byte);
        } else {
          // 中文字符处理（简化）
          if (i + 1 < byteArray.length) {
            const byte2 = byteArray[i + 1];
            // 查找对应的中文字符
            for (const [char, gbkBytes] of Object.entries(chineseGBKMap)) {
              if (gbkBytes[0] === byte && gbkBytes[1] === byte2) {
                str += char;
                i++; // 跳过下一个字节
                break;
              }
            }
          }
        }
      }
      return str;
    } else {
      throw new Error('Unsupported encoding: ' + this.encoding);
    }
  };

  // 导出到全局
  if (typeof module !== 'undefined' && module.exports) {
    module.exports = { TextEncoder, TextDecoder };
  } else {
    global.TextEncoder = TextEncoder;
    global.TextDecoder = TextDecoder;
  }

})(typeof global !== 'undefined' ? global : this);
