// pages/sampling/print-preview.js
const app = getApp()

Page({
  data: {
    printData: null,
    canvasWidth: 300,
    canvasHeight: 240
  },

  onLoad(options) {
    if (options.data) {
      try {
        const printData = JSON.parse(decodeURIComponent(options.data))
        this.setData({ printData })
        
        // 延迟绘制，确保页面加载完成
        setTimeout(() => {
          this.drawPrintPreview()
        }, 500)
      } catch (error) {
        console.error('解析打印数据失败:', error)
        app.showToast('数据解析失败')
      }
    }
  },

  // 绘制打印预览
  drawPrintPreview() {
    const ctx = wx.createCanvasContext('printCanvas', this)
    const { printData } = this.data
    
    if (!printData || !printData.content) {
      return
    }
    
    // 设置画布背景
    ctx.setFillStyle('#ffffff')
    ctx.fillRect(0, 0, this.data.canvasWidth, this.data.canvasHeight)
    
    // 绘制边框
    ctx.setStrokeStyle('#000000')
    ctx.setLineWidth(2)
    ctx.strokeRect(0, 0, this.data.canvasWidth, this.data.canvasHeight)
    
    // 绘制内容
    printData.content.forEach(item => {
      if (item.type === 'text') {
        this.drawText(ctx, item)
      } else if (item.type === 'qrcode') {
        this.drawQRCode(ctx, item)
      }
    })
    
    ctx.draw()
  },

  // 绘制文本
  drawText(ctx, item) {
    ctx.setFillStyle('#000000')
    ctx.setFontSize(item.fontSize || 12)
    
    // 转换坐标 (mm to px, 假设1mm = 4px)
    const x = (item.x || 0) * 4
    const y = (item.y || 0) * 4
    
    ctx.fillText(item.text, x, y)
  },

  // 绘制二维码占位符
  drawQRCode(ctx, item) {
    const x = (item.x || 0) * 4
    const y = (item.y || 0) * 4
    const size = (item.size || 20) * 4
    
    // 绘制二维码边框
    ctx.setStrokeStyle('#000000')
    ctx.setLineWidth(1)
    ctx.strokeRect(x, y, size, size)
    
    // 绘制二维码内容（简化版）
    ctx.setFillStyle('#000000')
    for (let i = 0; i < 8; i++) {
      for (let j = 0; j < 8; j++) {
        if ((i + j) % 2 === 0) {
          const cellSize = size / 8
          ctx.fillRect(x + i * cellSize, y + j * cellSize, cellSize, cellSize)
        }
      }
    }
    
    // 添加二维码文本说明
    ctx.setFillStyle('#666666')
    ctx.setFontSize(8)
    ctx.fillText('二维码', x, y + size + 12)
  },

  // 保存图片
  saveImage() {
    wx.canvasToTempFilePath({
      canvasId: 'printCanvas',
      success: (res) => {
        wx.saveImageToPhotosAlbum({
          filePath: res.tempFilePath,
          success: () => {
            app.showToast('已保存到相册', 'success')
          },
          fail: (error) => {
            console.error('保存图片失败:', error)
            if (error.errMsg.includes('auth')) {
              wx.showModal({
                title: '提示',
                content: '需要授权访问相册才能保存图片',
                success: (res) => {
                  if (res.confirm) {
                    wx.openSetting()
                  }
                }
              })
            } else {
              app.showToast('保存失败')
            }
          }
        })
      },
      fail: (error) => {
        console.error('生成图片失败:', error)
        app.showToast('生成图片失败')
      }
    }, this)
  },

  // 分享图片
  shareImage() {
    wx.canvasToTempFilePath({
      canvasId: 'printCanvas',
      success: (res) => {
        wx.showShareImageMenu({
          path: res.tempFilePath,
          success: () => {
            console.log('分享成功')
          },
          fail: (error) => {
            console.error('分享失败:', error)
            app.showToast('分享失败')
          }
        })
      },
      fail: (error) => {
        console.error('生成图片失败:', error)
        app.showToast('生成图片失败')
      }
    }, this)
  },

  // 重新打印
  rePrint() {
    wx.showModal({
      title: '重新打印',
      content: '是否重新尝试连接打印机？',
      success: (res) => {
        if (res.confirm) {
          // 返回上一页并重新触发打印
          wx.navigateBack()
        }
      }
    })
  }
})
