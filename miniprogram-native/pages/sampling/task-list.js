// pages/sampling/task-list.js
const app = getApp()

Page({
  data: {
    searchKeyword: '',
    taskList: [],
    stats: {
      total: 0,
      pending: 0,
      inProgress: 0,
      completed: 0
    },
    filterTags: [],
    loading: true,
    loadingMore: false,
    hasMore: true,
    page: 1,
    pageSize: 10,
    
    // 筛选相关
    showFilterModal: false,
    statusOptions: [
      { label: '全部', value: '', selected: true },
      { label: '待开始', value: 'pending', selected: false },
      { label: '进行中', value: 'in_progress', selected: false },
      { label: '已完成', value: 'completed', selected: false },
      { label: '已取消', value: 'cancelled', selected: false }
    ],
    filterStartDate: '',
    filterEndDate: '',
    currentFilter: {},
    expandedTasks: {} // 记录展开的任务ID
  },

  onLoad() {
    console.log('任务列表页面加载')
    this.loadData()
  },

  onShow() {
    // 检查登录状态
    if (!app.globalData.isLoggedIn) {
      wx.reLaunch({
        url: '/pages/login/login'
      })
      return
    }
  },

  onPullDownRefresh() {
    this.refreshData().finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  onReachBottom() {
    if (this.data.hasMore && !this.data.loadingMore) {
      this.loadMore()
    }
  },

  // 加载数据
  async loadData(isRefresh = false) {
    if (isRefresh) {
      this.setData({
        page: 1,
        hasMore: true,
        taskList: []
      })
    }

    this.setData({ loading: !isRefresh })

    try {
      const { page, pageSize, searchKeyword, currentFilter } = this.data

      // 构建查询参数
      const params = {
        page,
        size: pageSize, // 后端接口使用size参数
        task_name: searchKeyword, // 后端使用task_name参数进行搜索
        ...currentFilter
      }

      const [tasksResult, statsResult] = await Promise.all([
        this.loadTasks(params),
        this.loadStats()
      ])

      const newTaskList = isRefresh ? tasksResult.items : [...this.data.taskList, ...tasksResult.items]

      this.setData({
        taskList: newTaskList,
        hasMore: tasksResult.hasMore,
        stats: statsResult,
        page: page + 1
      })

    } catch (error) {
      console.error('加载任务列表失败:', error)
      app.showToast('加载失败，请重试')
    } finally {
      this.setData({ loading: false })
    }
  },

  // 加载任务列表 - 使用我的任务接口
  loadTasks(params) {
    return app.request({
      url: '/sampling/task/my-tasks', // 使用H5相同的接口
      method: 'GET'
    }).then(result => {
      if (result.code === 200) {
        let taskList = result.data || result.rows || []

        // 如果data是对象且包含rows，则使用rows
        if (typeof result.data === 'object' && result.data.rows) {
          taskList = result.data.rows
        }

        // 确保taskList是数组
        if (!Array.isArray(taskList)) {
          console.warn('任务数据不是数组格式:', taskList)
          taskList = []
        }

        console.log('我的任务原始数据:', taskList)

        // 根据搜索条件过滤
        if (params.task_name) {
          taskList = taskList.filter(task =>
            task.taskName && task.taskName.includes(params.task_name)
          )
        }

        // 映射数据结构
        const items = taskList.map(task => ({
          ...task,
          // 映射字段名以匹配前端显示
          title: task.taskName || '未命名任务',
          code: task.taskCode || '无编号',
          location: task.samplingLocation || '未指定',
          assignee: task.responsibleUserName || '未分配',
          planDate: this.formatDate(task.plannedStartDate || task.plannedEndDate),
          // 状态和进度
          statusText: this.getStatusText(task.status),
          progress: 0, // 暂时设为0
          sampleCount: task.sampleCount || 0,
          // 保留原始数据
          taskGroups: task.taskGroups || []
        }))

        console.log('处理后的任务数据:', items)

        return {
          items,
          hasMore: false
        }
      } else {
        throw new Error(result.message || '获取任务列表失败')
      }
    }).catch(error => {
      console.error('加载任务失败:', error)
      // 返回模拟数据
      return {
        items: this.getMockTasks(),
        hasMore: false
      }
    })
  },

  // 加载统计数据
  loadStats() {
    return app.request({
      url: '/sampling/task/stats', // TODO: 后端暂无此接口
      method: 'GET'
    }).catch(error => {
      console.error('加载统计数据失败:', error)
      // 返回模拟数据
      return {
        total: 15,
        pending: 5,
        inProgress: 8,
        completed: 2
      }
    })
  },

  // 获取模拟数据
  getMockTasks() {
    return [
      {
        id: 1,
        title: '水质采样任务-001',
        code: 'WQ20240115001',
        location: '北京市朝阳区某工厂',
        status: 'in_progress',
        statusText: '进行中',
        assignee: '张三',
        planDate: '2024-01-15',
        completedSamples: 3,
        totalSamples: 5,
        progress: 60,
        sampleCount: 5
      },
      {
        id: 2,
        title: '土壤采样任务-002',
        code: 'SL20240115002',
        location: '北京市海淀区某地块',
        status: 'pending',
        statusText: '待开始',
        assignee: '李四',
        planDate: '2024-01-16',
        completedSamples: 0,
        totalSamples: 8,
        progress: 0,
        sampleCount: 8
      }
    ]
  },

  // 获取状态文本
  getStatusText(status) {
    const statusMap = {
      0: '待执行',
      1: '执行中',
      2: '已完成',
      3: '已取消',
      // 兼容字符串状态
      'pending': '待执行',
      'in_progress': '执行中',
      'completed': '已完成',
      'cancelled': '已取消'
    }
    return statusMap[status] || '未知'
  },

  // 格式化日期
  formatDate(dateStr) {
    if (!dateStr) return ''
    const date = new Date(dateStr)
    return `${date.getMonth() + 1}月${date.getDate()}日`
  },

  // 搜索输入
  onSearchInput(e) {
    this.setData({
      searchKeyword: e.detail.value
    })
  },

  // 执行搜索
  onSearch() {
    this.refreshData()
  },

  // 清除搜索
  clearSearch() {
    this.setData({
      searchKeyword: ''
    })
    this.refreshData()
  },

  // 刷新数据
  refreshData() {
    return this.loadData(true)
  },

  // 加载更多
  loadMore() {
    if (this.data.loadingMore || !this.data.hasMore) return

    this.setData({ loadingMore: true })
    this.loadData().finally(() => {
      this.setData({ loadingMore: false })
    })
  },

  // 开始执行任务（针对分组）
  async startTask(e) {
    if (e && e.stopPropagation) {
      e.stopPropagation() // 阻止事件冒泡
    }
    const groupId = e.currentTarget.dataset.id

    try {
      await app.request({
        url: `/sampling/task-group/${groupId}/status`, // 使用分组状态更新接口
        method: 'PUT',
        data: 1 // 1表示执行中状态
      })

      app.showToast('任务已开始执行', 'success')
      this.refreshData()

    } catch (error) {
      console.error('开始执行任务失败:', error)
      app.showToast('操作失败，请重试')
    }
  },

  // 完成任务（针对分组）
  async completeTask(e) {
    if (e && e.stopPropagation) {
      e.stopPropagation() // 阻止事件冒泡
    }
    const groupId = e.currentTarget.dataset.id

    try {
      await app.request({
        url: `/sampling/task-group/${groupId}/status`, // 使用分组状态更新接口
        method: 'PUT',
        data: 2 // 2表示已完成状态
      })

      app.showToast('任务已完成', 'success')
      this.refreshData()

    } catch (error) {
      console.error('完成任务失败:', error)
      app.showToast('操作失败，请重试')
    }
  },

  // 继续任务
  continueTask(e) {
    const taskId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/sampling/task-detail?id=${taskId}`
    })
  },

  // 切换任务展开状态
  async toggleTaskExpand(e) {
    const taskId = e.currentTarget.dataset.id
    const expandedTasks = { ...this.data.expandedTasks }

    if (expandedTasks[taskId]) {
      // 收起
      delete expandedTasks[taskId]
    } else {
      // 展开，加载分组信息
      expandedTasks[taskId] = true

      // 查找对应的任务
      const task = this.data.taskList.find(t => t.id === taskId)
      if (task && (!task.taskGroups || task.taskGroups.length === 0)) {
        try {
          // 加载任务分组
          const result = await app.request({
            url: `/sampling/task-group/task/${taskId}`,
            method: 'GET'
          })

          if (result.code === 200) {
            const taskGroups = (result.data || []).map(group => ({
              ...group,
              statusText: this.getStatusText(group.status)
            }))
            // 更新任务的分组信息
            const taskList = this.data.taskList.map(t => {
              if (t.id === taskId) {
                return { ...t, taskGroups }
              }
              return t
            })
            this.setData({ taskList })
          }
        } catch (error) {
          console.error('加载任务分组失败:', error)
          app.showToast('加载分组信息失败')
        }
      }
    }

    this.setData({ expandedTasks })
  },

  // 跳转到分组详情（样品管理）
  goToGroupDetail(e) {
    if (e && e.stopPropagation) {
      e.stopPropagation() // 阻止事件冒泡
    }
    const groupId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/sampling/task-detail?id=${groupId}`
    })
  },

  // 样品管理
  handleSamplingManagement(e) {
    if (e && e.stopPropagation) {
      e.stopPropagation() // 阻止事件冒泡
    }
    const groupId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/sampling/task-detail?id=${groupId}`
    })
  },

  // 跳转到任务详情（保留兼容性）
  goToTaskDetail(e) {
    // 如果是点击任务头部，则展开/收起
    // 这个方法现在主要用于兼容性
    this.toggleTaskExpand(e)
  },

  // 显示筛选弹窗
  showFilter() {
    this.setData({
      showFilterModal: true
    })
  },

  // 隐藏筛选弹窗
  hideFilter() {
    this.setData({
      showFilterModal: false
    })
  },

  // 切换状态筛选
  toggleStatusFilter(e) {
    const value = e.currentTarget.dataset.value
    const statusOptions = this.data.statusOptions.map(option => ({
      ...option,
      selected: option.value === value
    }))
    
    this.setData({ statusOptions })
  },

  // 开始日期选择
  onStartDateChange(e) {
    this.setData({
      filterStartDate: e.detail.value
    })
  },

  // 结束日期选择
  onEndDateChange(e) {
    this.setData({
      filterEndDate: e.detail.value
    })
  },

  // 重置筛选
  resetFilter() {
    this.setData({
      statusOptions: this.data.statusOptions.map((option, index) => ({
        ...option,
        selected: index === 0
      })),
      filterStartDate: '',
      filterEndDate: ''
    })
  },

  // 应用筛选
  applyFilter() {
    const { statusOptions, filterStartDate, filterEndDate } = this.data
    const selectedStatus = statusOptions.find(option => option.selected)
    
    const filter = {}
    if (selectedStatus && selectedStatus.value) {
      filter.status = selectedStatus.value
    }
    if (filterStartDate) {
      filter.startDate = filterStartDate
    }
    if (filterEndDate) {
      filter.endDate = filterEndDate
    }

    // 生成筛选标签
    const filterTags = []
    if (selectedStatus && selectedStatus.value) {
      filterTags.push({
        key: 'status',
        label: selectedStatus.label,
        active: true
      })
    }
    if (filterStartDate || filterEndDate) {
      const dateRange = `${filterStartDate || '开始'} - ${filterEndDate || '结束'}`
      filterTags.push({
        key: 'date',
        label: dateRange,
        active: true
      })
    }

    this.setData({
      currentFilter: filter,
      filterTags,
      showFilterModal: false
    })

    this.refreshData()
  },

  // 切换筛选标签
  toggleFilterTag(e) {
    const key = e.currentTarget.dataset.key
    const filterTags = this.data.filterTags.filter(tag => tag.key !== key)
    
    // 重置对应的筛选条件
    const currentFilter = { ...this.data.currentFilter }
    if (key === 'status') {
      delete currentFilter.status
      this.setData({
        statusOptions: this.data.statusOptions.map((option, index) => ({
          ...option,
          selected: index === 0
        }))
      })
    } else if (key === 'date') {
      delete currentFilter.startDate
      delete currentFilter.endDate
      this.setData({
        filterStartDate: '',
        filterEndDate: ''
      })
    }

    this.setData({
      filterTags,
      currentFilter
    })

    this.refreshData()
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止事件冒泡
  }
})
