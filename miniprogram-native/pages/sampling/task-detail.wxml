<!--pages/sampling/task-detail.wxml-->
<view class="container">
  <!-- 头部信息 -->
  <view class="header">
    <view class="header-title">采样任务</view>
    <view class="task-number">分组编号：{{task.groupCode || task.code || 'GROUP001'}}</view>
    <view class="task-status-header">
      <text class="status-label">任务状态：</text>
      <view class="status-badge status-{{task.status || 0}}">
        <text>{{task.statusText || '待执行'}}</text>
      </view>
    </view>
  </view>

  <!-- Tab切换 -->
  <view class="tab-container">
    <view class="tabs">
      <view
        class="tab-item {{activeTab === 'task' ? 'active' : ''}}"
        bindtap="switchTab"
        data-tab="task"
      >
        任务信息
      </view>
      <view
        class="tab-item {{activeTab === 'samples' ? 'active' : ''}}"
        bindtap="switchTab"
        data-tab="samples"
      >
        样品管理
      </view>
      <view
        class="tab-item {{activeTab === 'point' ? 'active' : ''}}"
        bindtap="switchTab"
        data-tab="point"
      >
        点位信息
      </view>
    </view>

    <!-- Tab内容 -->
    <view class="tab-content">
      <!-- 任务信息 -->
      <view wx:if="{{activeTab === 'task'}}" class="content">
        <view class="section">
          <view class="section-title">基础信息</view>
          <view class="info-grid">
            <view class="info-item">
              <text class="label">分组编号：</text>
              <text class="value">{{task.groupCode || task.code || '-'}}</text>
            </view>
            <view class="info-item">
              <text class="label">任务名称：</text>
              <text class="value">{{task.taskName || task.title || '-'}}</text>
            </view>
            <view class="info-item">
              <text class="label">项目名称：</text>
              <text class="value">{{task.projectName || '-'}}</text>
            </view>
            <view class="info-item">
              <text class="label">客户名称：</text>
              <text class="value">{{task.customerName || '-'}}</text>
            </view>
            <view class="info-item">
              <text class="label">周期序号：</text>
              <text class="value">{{task.cycleNumber || '-'}}</text>
            </view>
            <view class="info-item">
              <text class="label">周期类型：</text>
              <text class="value">{{task.cycleType || '-'}}</text>
            </view>
            <view class="info-item">
              <text class="label">检测类别：</text>
              <text class="value">{{task.detectionCategory || '-'}}</text>
            </view>
            <view class="info-item">
              <text class="label">点位名称：</text>
              <text class="value">{{task.pointName || '-'}}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 样品管理 -->
      <view wx:if="{{activeTab === 'samples'}}" class="content">
        <!-- 样品统计 -->
        <view class="section">
          <view class="section-title">样品统计</view>
          <view class="statistics-grid">
            <view class="stat-item">
              <view class="stat-number">{{sampleStatistics.totalCount || 0}}</view>
              <view class="stat-label">总数量</view>
            </view>
            <view class="stat-item pending">
              <view class="stat-number">{{sampleStatistics.pendingCount || 0}}</view>
              <view class="stat-label">待采集</view>
            </view>
            <view class="stat-item collected">
              <view class="stat-number">{{sampleStatistics.collectedCount || 0}}</view>
              <view class="stat-label">已采集</view>
            </view>
            <view class="stat-item submitted">
              <view class="stat-number">{{sampleStatistics.submittedCount || 0}}</view>
              <view class="stat-label">已送检</view>
            </view>
          </view>
        </view>

        <!-- 样品记录列表 -->
        <view class="section">
          <view class="section-title">样品记录</view>
          <view wx:if="{{sampleList.length === 0}}" class="empty-state">
            <image class="empty-icon" src="/static/icons/empty.png" mode="aspectFit"></image>
            <text class="empty-text">暂无样品记录</text>
            <text class="empty-tip">请确保网络连接正常，或联系管理员</text>
          </view>
          <view wx:else class="sample-list">
            <view
              wx:for="{{sampleList}}"
              wx:key="id"
              class="sample-item"
            >
              <view class="sample-header">
                <view class="sample-number">样品序号：{{item.sampleNumber || (index + 1)}}</view>
                <view class="sample-status status-{{item.status}}">
                  <text>{{item.statusText || '待采集'}}</text>
                </view>
              </view>

              <view class="sample-info">
                <view class="info-row">
                  <text class="label">样品类型：</text>
                  <text class="value">{{item.sampleType || '-'}}</text>
                </view>
                <view class="info-row">
                  <text class="label">样品来源：</text>
                  <text class="value">{{item.sampleSource || '-'}}</text>
                </view>
                <view class="info-row">
                  <text class="label">点位名称：</text>
                  <text class="value">{{item.pointName || '-'}}</text>
                </view>
                <view class="info-row">
                  <text class="label">采样周期：</text>
                  <text class="value">{{item.cycleNumber}}({{item.cycleType}})</text>
                </view>
                <view class="info-row">
                  <text class="label">检测类别：</text>
                  <text class="value">{{item.detectionCategory || '-'}}</text>
                </view>
                <view class="info-row">
                  <text class="label">检测参数：</text>
                  <text class="value">{{item.detectionParameter || '-'}}</text>
                </view>
                <view class="info-row">
                  <text class="label">检测方法：</text>
                  <text class="value">{{item.detectionMethod || '-'}}</text>
                </view>
                <view class="info-row">
                  <text class="label">采集时间：</text>
                  <text class="value">{{item.collectionTime || '-'}}</text>
                </view>
              </view>

              <!-- 操作按钮 -->
              <view class="sample-actions">
                <button
                  wx:if="{{item.status === 0}}"
                  class="sample-action-btn primary"
                  bindtap="handleSampleCollect"
                  data-id="{{item.id}}"
                >
                  采集
                </button>
                <button
                  wx:if="{{item.status === 1}}"
                  class="sample-action-btn warning"
                  bindtap="handleSampleSubmit"
                  data-id="{{item.id}}"
                >
                  送检
                </button>
              </view>

              <!-- 瓶组信息 -->
              <view wx:if="{{item.bottleGroups && item.bottleGroups.length > 0}}" class="bottle-groups">
                <view class="bottle-groups-title">关联瓶组</view>
                <view class="bottle-list">
                  <view
                    wx:for="{{item.bottleGroups}}"
                    wx:for-item="bottle"
                    wx:key="id"
                    class="bottle-item"
                  >
                    <view class="bottle-info">
                      <view class="bottle-name">{{bottle.bottleGroupCode || ('瓶组' + bottle.id)}}</view>
                      <view class="bottle-status status-{{bottle.status}}">
                        <text wx:if="{{bottle.status === 0}}">待采集</text>
                        <text wx:elif="{{bottle.status === 1}}">已采集</text>
                        <text wx:elif="{{bottle.status === 2}}">已送检</text>
                        <text wx:else>未知</text>
                      </view>
                    </view>
                    <view class="bottle-details">
                      <view class="bottle-detail-row">
                        <text class="detail-label">类型：</text>
                        <text class="detail-value">{{bottle.bottleType || '默认瓶组'}}</text>
                      </view>
                      <view class="bottle-detail-row">
                        <text class="detail-label">容量：</text>
                        <text class="detail-value">{{bottle.bottleVolume || '-'}}</text>
                      </view>
                      <view class="bottle-detail-row">
                        <text class="detail-label">检测方法：</text>
                        <text class="detail-value">{{bottle.detectionMethod || '-'}}</text>
                      </view>
                    </view>
                    <view class="bottle-actions">
                      <button
                        wx:if="{{bottle.status === 1}}"
                        class="sample-action-btn warning"
                        bindtap="handleBottleSubmit"
                        data-id="{{bottle.id}}"
                      >
                        送检
                      </button>
                      <button
                        class="sample-action-btn info"
                        bindtap="handleBottlePrint"
                        data-bottle="{{bottle}}"
                      >
                        打印
                      </button>
                    </view>
                  </view>
                </view>
              </view>

              <!-- 加载瓶组按钮 -->
              <view wx:if="{{!item.bottleGroupsLoaded}}" class="load-bottles">
                <button
                  class="load-bottles-btn"
                  bindtap="loadBottleGroups"
                  data-sample="{{item}}"
                >
                  查看瓶组信息
                </button>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 点位信息 -->
      <view wx:if="{{activeTab === 'point'}}" class="content">
        <view class="section">
          <view class="section-title">点位信息</view>
          <view class="info-grid">
            <view class="info-item">
              <text class="label">点位名称：</text>
              <text class="value">{{task.pointName || '-'}}</text>
            </view>
            <view class="info-item">
              <text class="label">点位编号：</text>
              <text class="value">{{task.pointCode || '-'}}</text>
            </view>
            <view class="info-item">
              <text class="label">点位类型：</text>
              <text class="value">{{task.pointType || '-'}}</text>
            </view>
            <view class="info-item">
              <text class="label">点位描述：</text>
              <text class="value">{{task.pointDescription || '-'}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部信息 -->
  <view class="footer">
    <text>扫码时间：{{currentTime}}</text>
    <text>采样管理系统</text>
  </view>
</view>

<!-- 加载状态 -->
<view wx:if="{{loading}}" class="loading-overlay">
  <view class="loading-content">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>
</view>
