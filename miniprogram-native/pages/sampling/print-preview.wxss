/* pages/sampling/print-preview.wxss */

.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 40rpx;
}

/* 头部样式 */
.header {
  background: linear-gradient(135deg, #409EFF, #67C23A);
  color: white;
  padding: 40rpx 30rpx 30rpx;
  text-align: center;
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.header-subtitle {
  font-size: 26rpx;
  opacity: 0.9;
}

/* 预览容器 */
.preview-container {
  background: white;
  margin: 30rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.preview-wrapper {
  padding: 40rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #f8f9fa;
}

.print-canvas {
  border: 2rpx solid #409EFF;
  border-radius: 8rpx;
  background: white;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* 打印信息 */
.print-info {
  padding: 30rpx;
  border-top: 1rpx solid #e9ecef;
}

.info-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #303133;
  margin-bottom: 20rpx;
}

.info-list {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.info-item {
  padding: 12rpx 16rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
  border-left: 4rpx solid #409EFF;
}

.info-text {
  font-size: 24rpx;
  color: #606266;
  line-height: 1.4;
}

/* 操作按钮 */
.actions {
  display: flex;
  gap: 20rpx;
  padding: 0 30rpx;
  margin-top: 30rpx;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  border: none;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn.primary {
  background: #409EFF;
  color: white;
}

.action-btn.secondary {
  background: #f8f9fa;
  color: #606266;
  border: 1rpx solid #e4e7ed;
}

.action-btn.secondary:active {
  background: #e9ecef;
}

/* 使用说明 */
.instructions {
  background: white;
  margin: 30rpx;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.instruction-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #303133;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
}

.instruction-title::before {
  content: '';
  width: 6rpx;
  height: 28rpx;
  background: #409EFF;
  border-radius: 3rpx;
  margin-right: 12rpx;
}

.instruction-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.instruction-item {
  display: flex;
  align-items: flex-start;
  line-height: 1.5;
}

.instruction-number {
  font-size: 24rpx;
  color: #409EFF;
  font-weight: bold;
  min-width: 32rpx;
  margin-right: 12rpx;
}

.instruction-text {
  font-size: 24rpx;
  color: #606266;
  flex: 1;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .actions {
    flex-direction: column;
    gap: 16rpx;
  }
  
  .action-btn {
    height: 88rpx;
  }
  
  .preview-wrapper {
    padding: 30rpx 20rpx;
  }
}
