// pages/sampling/task-detail.js
const app = getApp()
const { TextEncoder, TextDecoder } = require('../../utils/text-encoding.js')

Page({
  data: {
    taskId: '',
    task: {},
    sampleList: [],
    sampleStatistics: {
      totalCount: 0,
      pendingCount: 0,
      collectedCount: 0,
      submittedCount: 0
    },
    loading: true,
    activeTab: 'task', // 当前激活的Tab
    currentTime: ''
  },

  onLoad(options) {
    console.log('任务详情页面加载')
    const taskId = options.id
    if (taskId) {
      this.setData({
        taskId,
        currentTime: new Date().toLocaleString()
      })
      this.loadTaskDetail(taskId)
    } else {
      this.loadMockData()
    }
  },

  onShow() {
    // 检查登录状态
    if (!app.globalData.isLoggedIn) {
      wx.reLaunch({
        url: '/pages/login/login'
      })
      return
    }
  },

  onPullDownRefresh() {
    this.loadTaskDetail(this.data.taskId).finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  // 加载任务详情
  async loadTaskDetail(taskId) {
    this.setData({ loading: true })

    try {
      const [taskData, samplesData] = await Promise.all([
        this.loadTaskInfo(taskId),
        this.loadSampleList(taskId)
      ])

      this.setData({
        task: taskData,
        sampleList: samplesData
      })

    } catch (error) {
      console.error('加载任务详情失败:', error)
      app.showToast('加载失败，请重试')
      this.loadMockData()
    } finally {
      this.setData({ loading: false })
    }
  },

  // 加载任务分组信息 - 这里的taskId实际上是groupId
  loadTaskInfo(groupId) {
    return app.request({
      url: `/sampling/task-group/${groupId}`, // 使用分组详情接口
      method: 'GET'
    }).then(result => {
      if (result.code === 200) {
        const data = result.data
        return {
          ...data,
          // 映射字段名以匹配前端显示
          title: data.taskName || '未命名任务',
          code: data.groupCode || data.taskCode || '无编号',
          location: data.samplingLocation || '未指定',
          assignee: data.responsibleUserName || '未分配',
          planDate: this.formatDate(data.plannedStartDate || data.plannedEndDate),
          // 状态和进度
          statusText: this.getStatusText(data.status),
          progress: 0, // 暂时设为0
          // 分组特有信息
          groupCode: data.groupCode,
          projectName: data.projectName,
          customerName: data.customerName,
          cycleNumber: data.cycleNumber,
          cycleType: data.cycleType,
          detectionCategory: data.detectionCategory,
          pointName: data.pointName
        }
      } else {
        throw new Error(result.message || '获取任务信息失败')
      }
    }).catch(error => {
      console.error('加载任务信息失败:', error)
      throw error
    })
  },

  // 加载样品列表 - 根据分组ID获取样品记录
  async loadSampleList(groupId) {
    try {
      // 先尝试获取现有的样品记录
      const result = await app.request({
        url: `/sampling/sample-records/group/${groupId}`, // 使用正确的接口路径
        method: 'GET'
      })

      if (result.code === 200) {
        let sampleList = result.data || []

        // 如果没有样品记录，尝试生成
        if (sampleList.length === 0) {
          console.log('没有找到样品记录，尝试生成...')
          try {
            const generateResult = await app.request({
              url: `/sampling/sample-records/generate/group/${groupId}`,
              method: 'POST'
            })

            if (generateResult.code === 200) {
              sampleList = generateResult.data || []
              console.log(`成功生成 ${sampleList.length} 条样品记录`)
            }
          } catch (generateError) {
            console.warn('生成样品记录失败:', generateError)
            // 生成失败不影响页面显示，继续使用空列表
          }
        }

        return sampleList.map(sample => ({
          ...sample,
          statusText: this.getSampleStatusText(sample.status),
          bottleGroupsLoaded: false,
          bottleGroupsLoading: false,
          bottleGroups: []
        }))
      } else {
        console.warn('获取样品列表失败:', result.message)
        return []
      }
    } catch (error) {
      console.error('加载样品列表失败:', error)
      return []
    }
  },

  // 加载模拟数据
  loadMockData() {
    const mockTask = {
      id: 1,
      title: '水质采样任务-001',
      code: 'WQ20240115001',
      location: '北京市朝阳区某工厂排污口',
      status: 'in_progress',
      statusText: '进行中',
      assignee: '张三',
      planDate: '01-15',
      completedSamples: 2,
      totalSamples: 5,
      progress: 40
    }

    const mockSamples = [
      {
        id: 1,
        name: '进水口水样',
        code: 'WS001',
        type: '地表水',
        status: 'completed',
        statusText: '已完成'
      },
      {
        id: 2,
        name: '出水口水样',
        code: 'WS002',
        type: '地表水',
        status: 'collected',
        statusText: '已采集'
      },
      {
        id: 3,
        name: '中段水样',
        code: 'WS003',
        type: '地表水',
        status: 'pending',
        statusText: '待采集'
      }
    ]

    this.setData({
      task: mockTask,
      sampleList: mockSamples,
      loading: false
    })
  },

  // 获取状态文本
  getStatusText(status) {
    const statusMap = {
      0: '待执行',
      1: '执行中',
      2: '已完成',
      3: '已取消',
      // 兼容字符串状态
      'pending': '待执行',
      'in_progress': '执行中',
      'completed': '已完成',
      'cancelled': '已取消'
    }
    return statusMap[status] || '未知'
  },

  // 获取样品状态文本
  getSampleStatusText(status) {
    const statusMap = {
      0: '待采集',
      1: '已采集',
      2: '已送检'
    }
    return statusMap[status] || '未知'
  },

  // Tab切换
  switchTab(e) {
    const tab = e.currentTarget.dataset.tab
    this.setData({ activeTab: tab })

    // 如果切换到样品管理Tab，加载样品数据
    if (tab === 'samples' && this.data.sampleList.length === 0) {
      this.loadSampleData()
    }
  },

  // 加载样品数据和统计
  async loadSampleData() {
    const groupId = this.data.taskId
    if (!groupId) return

    try {
      // 加载样品记录
      const sampleList = await this.loadSampleList(groupId)

      // 计算统计数据
      const statistics = this.calculateSampleStatistics(sampleList)

      this.setData({
        sampleList: sampleList.map(sample => ({
          ...sample,
          statusText: this.getSampleStatusText(sample.status),
          bottleGroupsLoaded: false,
          bottleGroupsLoading: false
        })),
        sampleStatistics: statistics
      })
    } catch (error) {
      console.error('加载样品数据失败:', error)
      app.showToast('加载样品数据失败')
    }
  },

  // 计算样品统计
  calculateSampleStatistics(sampleList) {
    const stats = {
      totalCount: sampleList.length,
      pendingCount: 0,
      collectedCount: 0,
      submittedCount: 0
    }

    sampleList.forEach(sample => {
      switch (sample.status) {
        case 0:
          stats.pendingCount++
          break
        case 1:
          stats.collectedCount++
          break
        case 2:
          stats.submittedCount++
          break
      }
    })

    return stats
  },

  // 样品采集
  async handleSampleCollect(e) {
    const sampleId = e.currentTarget.dataset.id

    try {
      await app.request({
        url: `/sampling/sample-records/status/${sampleId}`, // 使用正确的接口路径
        method: 'PUT',
        data: 1 // 1表示已采集
      })

      app.showToast('样品采集成功', 'success')
      this.loadSampleData() // 重新加载数据

    } catch (error) {
      console.error('样品采集失败:', error)
      app.showToast('样品采集失败')
    }
  },

  // 样品送检
  async handleSampleSubmit(e) {
    const sampleId = e.currentTarget.dataset.id

    try {
      await app.request({
        url: `/sampling/sample-records/status/${sampleId}`, // 使用正确的接口路径
        method: 'PUT',
        data: 2 // 2表示已送检
      })

      app.showToast('样品送检成功', 'success')
      this.loadSampleData() // 重新加载数据

    } catch (error) {
      console.error('样品送检失败:', error)
      app.showToast('样品送检失败')
    }
  },

  // 加载瓶组信息
  async loadBottleGroups(e) {
    const sample = e.currentTarget.dataset.sample

    try {
      // 更新加载状态
      const sampleList = this.data.sampleList.map(item => {
        if (item.id === sample.id) {
          return { ...item, bottleGroupsLoading: true }
        }
        return item
      })
      this.setData({ sampleList })

      const result = await app.request({
        url: `/sampling/bottle-groups/sample/${sample.id}`, // 使用正确的接口路径
        method: 'GET'
      })

      if (result.code === 200) {
        const bottleGroups = result.data || []
        console.log(`样品 ${sample.id} 的瓶组信息:`, bottleGroups)

        // 更新样品的瓶组信息
        const updatedSampleList = this.data.sampleList.map(item => {
          if (item.id === sample.id) {
            return {
              ...item,
              bottleGroups,
              bottleGroupsLoaded: true,
              bottleGroupsLoading: false
            }
          }
          return item
        })

        this.setData({ sampleList: updatedSampleList })
        console.log('瓶组信息已更新到样品列表')
      } else {
        console.warn('获取瓶组信息失败:', result.message)
        app.showToast(result.message || '获取瓶组信息失败')
      }
    } catch (error) {
      console.error('加载瓶组信息失败:', error)
      app.showToast('加载瓶组信息失败')

      // 重置加载状态
      const sampleList = this.data.sampleList.map(item => {
        if (item.id === sample.id) {
          return { ...item, bottleGroupsLoading: false }
        }
        return item
      })
      this.setData({ sampleList })
    }
  },

  // 瓶组送检
  async handleBottleSubmit(e) {
    const bottleId = e.currentTarget.dataset.id

    try {
      await app.request({
        url: `/sampling/bottle-groups/status/${bottleId}`, // 使用正确的接口路径
        method: 'PUT',
        data: 2 // 2表示已送检
      })

      app.showToast('瓶组送检成功', 'success')
      this.loadSampleData() // 重新加载数据

    } catch (error) {
      console.error('瓶组送检失败:', error)
      app.showToast('瓶组送检失败')
    }
  },

  // 瓶组打印
  async handleBottlePrint(e) {
    console.log('瓶组打印方法被调用')
    const bottle = e.currentTarget.dataset.bottle
    console.log('瓶组数据:', bottle)

    if (!bottle) {
      wx.showToast({
        title: '瓶组信息不存在',
        icon: 'none'
      })
      return
    }

    // 准备打印数据
    const taskInfo = this.data.task  // 修复：使用正确的字段名
    const bottleGroup = {
      ...bottle,
      sortOrder: this.data.sampleList.findIndex(s => s.bottleGroups && s.bottleGroups.some(b => b.id === bottle.id)) + 1,
      totalCount: this.data.sampleList.length
    }

    console.log('打印数据准备:', { taskInfo, bottleGroup })

    // 跳转到打印预览页面
    const printData = {
      taskInfo: taskInfo,
      bottleGroup: bottleGroup
    }

    wx.navigateTo({
      url: `/pages/printer/preview?data=${encodeURIComponent(JSON.stringify(printData))}`
    })
  },

  // 原来的蓝牙打印方法（保留作为备用）
  async bluetoothPrintBackup(bottle) {
    try {
      // 找到当前瓶组所属的样品记录
      let sampleIndex = 0
      let totalSamples = this.data.sampleList.length

      // 遍历样品记录，找到包含当前瓶组的样品
      for (let i = 0; i < this.data.sampleList.length; i++) {
        const sample = this.data.sampleList[i]
        if (sample.bottleGroups && sample.bottleGroups.some(b => b.id === bottle.id)) {
          sampleIndex = i + 1 // 样品序号从1开始
          break
        }
      }

      // 构建样品编号：任务编号（样品序号/样品总数量）
      const taskCode = this.data.task.taskCode || this.data.task.groupCode || 'TASK-001'
      const sampleNumber = `${taskCode}（${sampleIndex}/${totalSamples}）`

      // 构建采样点位：点位名称 （周期{周期数}）
      const pointName = this.data.task.pointName || '采样点位'
      const cycleNumber = this.data.task.cycleNumber || 1
      const samplingPoint = `${pointName} （周期${cycleNumber}）`

      // 格式化采样日期
      const samplingDate = this.formatDate(new Date())

      // 构建打印内容
      const printContent = this.generatePrintContent({
        sampleCategory: bottle.bottleType || '水样',
        sampleNumber: sampleNumber,
        samplingDate: samplingDate,
        samplingPoint: samplingPoint,
        testItems: bottle.detectionMethod || '常规检测',
        container: bottle.bottleVolume || '500ml塑料瓶',
        storageMethod: '常温保存',
        bottleCode: bottle.bottleGroupCode || `瓶组${bottle.id}`,
        qrCodeText: `${sampleNumber}-${bottle.id}`
      })

      // 调用微信小程序打印接口
      console.log('准备调用打印接口，打印内容:', printContent)
      await this.printLabel(printContent)

    } catch (error) {
      console.error('打印失败:', error)
      app.showToast('打印失败: ' + error.message)
    }
  },

  // 生成打印内容
  generatePrintContent(data) {
    return {
      // 标签尺寸 (mm)
      width: 75,
      height: 60,
      // 打印内容
      content: [
        {
          type: 'text',
          text: '样品类别：' + data.sampleCategory,
          x: 2,
          y: 5,
          fontSize: 10,
          fontWeight: 'bold'
        },
        {
          type: 'text',
          text: '样品编号：' + data.sampleNumber,
          x: 2,
          y: 12,
          fontSize: 9
        },
        {
          type: 'text',
          text: '采样日期：' + data.samplingDate,
          x: 2,
          y: 19,
          fontSize: 9
        },
        {
          type: 'text',
          text: '采样点位：' + data.samplingPoint,
          x: 2,
          y: 26,
          fontSize: 9
        },
        {
          type: 'text',
          text: '检测项目：' + data.testItems,
          x: 2,
          y: 33,
          fontSize: 9
        },
        {
          type: 'text',
          text: '保存容器：' + data.container,
          x: 2,
          y: 40,
          fontSize: 9
        },
        {
          type: 'text',
          text: '保存方式：' + data.storageMethod,
          x: 2,
          y: 47,
          fontSize: 9
        },
        {
          type: 'text',
          text: '样品状态：☐待测 ☐合格 ☐不合格',
          x: 2,
          y: 54,
          fontSize: 8
        },
        {
          type: 'qrcode',
          text: data.qrCodeText,
          x: 55,
          y: 5,
          size: 20
        }
      ]
    }
  },

  // 调用微信小程序打印接口
  async printLabel(printContent) {
    console.log('printLabel方法被调用，内容:', printContent)

    // 直接进行蓝牙打印
    try {
      await this.bluetoothPrint(printContent)
    } catch (error) {
      console.error('蓝牙打印失败:', error)
      // 降级到预览模式
      wx.showModal({
        title: '打印失败',
        content: '蓝牙打印失败，是否查看打印预览？',
        confirmText: '查看预览',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            this.showPrintPreview(printContent)
          }
        }
      })
    }
  },

  // 蓝牙打印
  async bluetoothPrint(printContent) {
    try {
      // 获取已配置的打印机
      const connectedPrinter = wx.getStorageSync('connectedDevice')
      const printerSettings = wx.getStorageSync('printerSettings') || {
        paperWidth: 75,
        paperHeight: 60,
        printDensity: 'medium',
        printSpeed: 'normal',
        commandSet: 'CPCL'
      }

      if (!connectedPrinter) {
        throw new Error('未找到已配置的打印机')
      }

      // 1. 初始化蓝牙适配器
      await wx.openBluetoothAdapter()

      // 2. 直接连接已配置的打印机
      wx.showLoading({ title: '连接打印机...' })

      try {
        await wx.createBLEConnection({
          deviceId: connectedPrinter.deviceId
        })

        wx.hideLoading()
        wx.showLoading({ title: '正在打印...' })

        // 3. 发送打印数据
        await this.sendPrintDataToPrinter(connectedPrinter, printContent, printerSettings)

        wx.hideLoading()
        wx.showToast({
          title: '打印完成',
          icon: 'success'
        })

      } catch (connectError) {
        wx.hideLoading()
        console.error('连接打印机失败:', connectError)

        // 连接失败，提示用户重新配置
        wx.showModal({
          title: '连接失败',
          content: '无法连接到已配置的打印机，是否重新配置？',
          confirmText: '重新配置',
          cancelText: '取消',
          success: (res) => {
            if (res.confirm) {
              wx.navigateTo({
                url: '/pages/printer/config'
              })
            }
          }
        })
      }

    } catch (error) {
      wx.hideLoading()
      console.error('蓝牙打印失败:', error)

      // 降级到预览模式
      this.showPrintPreview(printContent)
    }
  },

  // 发送打印数据到打印机
  async sendPrintDataToPrinter(printer, content, settings) {
    try {
      // 检查打印机是否有服务信息
      if (!printer.serviceId || !printer.characteristicId) {
        throw new Error('打印机服务信息不完整，请重新配置')
      }

      console.log('打印机信息:', printer)
      console.log('打印内容:', content)
      console.log('打印设置:', settings)

      // 生成CPCL指令
      const printCommands = this.generateCPCLCommands(content, settings)
      console.log('生成的CPCL指令:', printCommands)

      // 发送打印指令
      await this.sendBLEData(printer, printCommands)

    } catch (error) {
      console.error('发送打印数据失败:', error)
      throw error
    }
  },

  // 生成CPCL打印指令
  generateCPCLCommands(content, settings) {
    const commands = []

    // CPCL标签开始 - 使用200DPI
    const labelHeight = Math.min(settings.paperHeight * 6, 400)
    const labelStart = `! 0 200 200 ${labelHeight} 1\r\n`
    commands.push(...this.stringToBytes(labelStart))

    // 添加GB2312编码声明（根据测试结果，这个有效）
    commands.push(...this.stringToBytes('ENCODING GB2312\r\n'))

    // 样品标签标题 - 使用字体3和中文
    commands.push(...this.stringToBytes('TEXT 3 0 5 10 '))
    const titleEncoder = new TextEncoder('gbk')
    const titleBytes = Array.from(titleEncoder.encode('样品标签'))
    commands.push(...titleBytes)
    commands.push(...this.stringToBytes('\r\n'))

    // 遍历打印内容
    let yPos = 35
    content.content.forEach((item, index) => {
      if (item.type === 'text') {
        // 使用字体3（支持中文且大小合适）
        const textCmd = `TEXT 3 0 5 ${yPos} `
        commands.push(...this.stringToBytes(textCmd))

        // 使用GBK编码处理中文内容
        const gbkEncoder = new TextEncoder('gbk')
        const textBytes = Array.from(gbkEncoder.encode(item.text))
        commands.push(...textBytes)
        commands.push(...this.stringToBytes('\r\n'))

        yPos += 22 // 行间距

      } else if (item.type === 'qrcode') {
        // 二维码 - 使用合适尺寸
        const qrX = Math.max(5, item.x * 4)
        const qrY = Math.max(yPos, item.y * 4)
        const qrCmd = `BARCODE QR ${qrX} ${qrY} M 2 U 4\r\n`
        commands.push(...this.stringToBytes(qrCmd))
        commands.push(...this.stringToBytes(`MA,${item.text}\r\n`))
        commands.push(...this.stringToBytes('ENDQR\r\n'))

        // 更新Y位置，为二维码留出空间
        yPos = Math.max(yPos, qrY + 80)
      }
    })

    // 打印标签
    commands.push(...this.stringToBytes('PRINT\r\n'))

    return new Uint8Array(commands)
  },

  // 字符串转字节数组（ASCII）
  stringToBytes(str) {
    const bytes = []
    for (let i = 0; i < str.length; i++) {
      bytes.push(str.charCodeAt(i))
    }
    return bytes
  },

  // 字符串转GBK编码（简化版）
  stringToGBK(str) {
    const bytes = []
    for (let i = 0; i < str.length; i++) {
      const char = str.charAt(i)
      const code = str.charCodeAt(i)

      if (code < 0x80) {
        // ASCII字符直接输出
        bytes.push(code)
      } else {
        // 中文字符处理
        // 对于CPCL打印机，尝试使用简化的中文处理
        // 这里使用一个简化的映射，实际应该使用完整的GBK码表
        const gbkBytes = this.chineseToGBK(char)
        if (gbkBytes.length > 0) {
          bytes.push(...gbkBytes)
        } else {
          // 如果无法转换，使用问号替代
          bytes.push(0x3F) // '?'
        }
      }
    }
    return bytes
  },

  // 简化的中文字符转GBK
  chineseToGBK(char) {
    // 常用中文字符的GBK编码映射（简化版）
    const commonChars = {
      '样': [0xD1, 0xF9],
      '品': [0xC6, 0xB7],
      '类': [0xC0, 0xE0],
      '别': [0xB1, 0xF0],
      '编': [0xB1, 0xE0],
      '号': [0xBA, 0xC5],
      '采': [0xB2, 0xC9],
      '日': [0xC8, 0xD5],
      '期': [0xC6, 0xDA],
      '点': [0xB5, 0xE3],
      '位': [0xCE, 0xBB],
      '检': [0xBC, 0xEC],
      '测': [0xB2, 0xE2],
      '项': [0xCF, 0xEE],
      '目': [0xC4, 0xBF],
      '保': [0xB1, 0xA3],
      '存': [0xB4, 0xE6],
      '容': [0xC8, 0xDD],
      '器': [0xC6, 0xF7],
      '方': [0xB7, 0xBD],
      '式': [0xCA, 0xBD],
      '状': [0xD7, 0xB4],
      '态': [0xCC, 0xAC],
      '待': [0xB4, 0xFD],
      '合': [0xBA, 0xCF],
      '格': [0xB8, 0xF1],
      '不': [0xB2, 0xBB],
      '水': [0xCB, 0xAE],
      '常': [0xB3, 0xA3],
      '规': [0xB9, 0xE6],
      '温': [0xCE, 0xC2],
      '瓶': [0xC6, 0xBF],
      '组': [0xD7, 0xE9],
      '周': [0xD6, 0xDC],
      '期': [0xC6, 0xDA]
    }

    if (commonChars[char]) {
      return commonChars[char]
    }

    // 如果不在常用字符中，尝试使用Unicode转换（简化处理）
    const code = char.charCodeAt(0)
    if (code >= 0x4E00 && code <= 0x9FFF) {
      // 中文字符范围，使用简化的双字节编码
      const high = Math.floor((code - 0x4E00) / 256) + 0xA1
      const low = ((code - 0x4E00) % 256) + 0xA1
      return [Math.min(high, 0xFE), Math.min(low, 0xFE)]
    }

    return [] // 无法转换
  },

  // 字符串转UTF-8编码
  stringToUTF8(str) {
    const bytes = []
    for (let i = 0; i < str.length; i++) {
      const code = str.charCodeAt(i)
      if (code < 0x80) {
        bytes.push(code)
      } else if (code < 0x800) {
        bytes.push(0xC0 | (code >> 6))
        bytes.push(0x80 | (code & 0x3F))
      } else {
        bytes.push(0xE0 | (code >> 12))
        bytes.push(0x80 | ((code >> 6) & 0x3F))
        bytes.push(0x80 | (code & 0x3F))
      }
    }
    return bytes
  },

  // 发送BLE数据
  async sendBLEData(device, data) {
    const maxChunkSize = 20 // BLE每次最大传输20字节

    console.log('开始发送数据，总长度:', data.length, '字节')

    for (let i = 0; i < data.length; i += maxChunkSize) {
      const chunk = data.slice(i, i + maxChunkSize)

      await wx.writeBLECharacteristicValue({
        deviceId: device.deviceId,
        serviceId: device.serviceId,
        characteristicId: device.characteristicId,
        value: chunk.buffer
      })

      // 每次发送后稍作延迟，避免数据丢失
      await new Promise(resolve => setTimeout(resolve, 100))
    }

    console.log('数据发送完成')
  },

  // 原有的搜索打印机方法（保留作为备用）
  async searchAndConnectPrinter(printContent) {
    try {
      // 2. 搜索蓝牙设备
      wx.showLoading({ title: '搜索打印机...' })

      await wx.startBluetoothDevicesDiscovery({
        services: [],
        allowDuplicatesKey: false
      })

      // 3. 获取已发现的设备
      setTimeout(async () => {
        try {
          const devices = await wx.getBluetoothDevices()
          wx.hideLoading()

          if (devices.devices.length === 0) {
            throw new Error('未找到蓝牙设备')
          }

          // 4. 显示设备选择列表
          this.showPrinterSelection(devices.devices, printContent)

        } catch (error) {
          wx.hideLoading()
          throw error
        }
      }, 3000)

    } catch (error) {
      wx.hideLoading()
      console.error('蓝牙打印失败:', error)

      if (error.errCode === 10001) {
        wx.showModal({
          title: '提示',
          content: '请先开启蓝牙功能',
          showCancel: false
        })
      } else {
        // 降级到预览模式
        this.showPrintPreview(printContent)
      }
    }
  },

  // 显示打印机选择
  showPrinterSelection(devices, printContent) {
    const printerDevices = devices.filter(device =>
      device.name && (
        device.name.toLowerCase().includes('printer') ||
        device.name.toLowerCase().includes('print') ||
        device.name.includes('打印')
      )
    )

    if (printerDevices.length === 0) {
      wx.showModal({
        title: '未找到打印机',
        content: '未发现可用的蓝牙打印机，是否查看打印预览？',
        success: (res) => {
          if (res.confirm) {
            this.showPrintPreview(printContent)
          }
        }
      })
      return
    }

    const deviceNames = printerDevices.map(device => device.name || '未知设备')

    wx.showActionSheet({
      itemList: deviceNames,
      success: (res) => {
        const selectedDevice = printerDevices[res.tapIndex]
        this.connectAndPrint(selectedDevice, printContent)
      }
    })
  },

  // 连接设备并打印
  async connectAndPrint(device, printContent) {
    try {
      wx.showLoading({ title: '连接打印机...' })

      // 连接设备
      await wx.createBLEConnection({
        deviceId: device.deviceId
      })

      // 获取服务
      const services = await wx.getBLEDeviceServices({
        deviceId: device.deviceId
      })

      // 这里需要根据具体打印机的服务和特征值进行配置
      // 由于不同打印机的协议不同，这里提供一个通用的框架

      wx.hideLoading()
      app.showToast('打印机连接成功')

      // 发送打印数据
      await this.sendPrintData(device.deviceId, printContent)

    } catch (error) {
      wx.hideLoading()
      console.error('连接打印机失败:', error)
      app.showToast('连接打印机失败')
    }
  },

  // 发送打印数据
  async sendPrintData(deviceId, printContent) {
    try {
      // 这里需要根据具体打印机协议转换打印内容
      // 不同品牌的打印机有不同的指令集

      // 示例：ESC/POS指令集
      const printCommands = this.generateESCPOSCommands(printContent)

      // 发送数据到打印机
      // 这里需要找到正确的服务UUID和特征值UUID

      app.showToast('打印任务已发送', 'success')

    } catch (error) {
      console.error('发送打印数据失败:', error)
      app.showToast('打印失败')
    }
  },

  // 生成ESC/POS打印指令
  generateESCPOSCommands(printContent) {
    // 这里需要根据ESC/POS协议生成打印指令
    // 由于比较复杂，这里只是一个示例框架
    const commands = []

    // 初始化打印机
    commands.push([0x1B, 0x40])

    // 设置字体大小
    commands.push([0x1B, 0x21, 0x00])

    // 打印文本内容
    printContent.content.forEach(item => {
      if (item.type === 'text') {
        const textBytes = this.stringToBytes(item.text + '\n')
        commands.push(textBytes)
      }
    })

    // 切纸
    commands.push([0x1D, 0x56, 0x00])

    return commands
  },

  // 字符串转字节数组
  stringToBytes(str) {
    const bytes = []
    for (let i = 0; i < str.length; i++) {
      bytes.push(str.charCodeAt(i))
    }
    return bytes
  },

  // 显示打印预览（降级方案）
  showPrintPreview(printContent) {
    wx.showModal({
      title: '打印预览',
      content: '由于未找到可用打印机，将显示打印预览。您可以截图保存或分享。',
      confirmText: '查看预览',
      success: (res) => {
        if (res.confirm) {
          // 跳转到打印预览页面
          wx.navigateTo({
            url: `/pages/sampling/print-preview?data=${encodeURIComponent(JSON.stringify(printContent))}`
          })
        }
      }
    })
  },

  // 格式化日期
  formatDate(date) {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
  },



  // 获取样品状态文本
  getSampleStatusText(status) {
    const statusMap = {
      'pending': '待采集',
      'collected': '已采集',
      'submitted': '已提交',
      'completed': '已完成'
    }
    return statusMap[status] || '未知'
  },

  // 格式化日期
  formatDate(dateStr) {
    if (!dateStr) return ''
    const date = new Date(dateStr)
    return `${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
  },

  // 开始任务
  async startTask() {
    try {
      app.showLoading('启动中...')
      
      await app.request({
        url: `/api/sampling/tasks/${this.data.taskId}/start`,
        method: 'POST'
      })
      
      app.showToast('任务已开始', 'success')
      this.loadTaskDetail(this.data.taskId)
      
    } catch (error) {
      console.error('开始任务失败:', error)
      app.showToast('操作失败，请重试')
    } finally {
      app.hideLoading()
    }
  },

  // 完成任务
  async completeTask() {
    // 检查是否所有样品都已完成
    const pendingSamples = this.data.sampleList.filter(sample => 
      sample.status !== 'completed'
    )

    if (pendingSamples.length > 0) {
      wx.showModal({
        title: '提示',
        content: `还有${pendingSamples.length}个样品未完成，确定要完成任务吗？`,
        success: (res) => {
          if (res.confirm) {
            this.performCompleteTask()
          }
        }
      })
    } else {
      this.performCompleteTask()
    }
  },

  // 执行完成任务
  async performCompleteTask() {
    try {
      app.showLoading('提交中...')
      
      await app.request({
        url: `/api/sampling/tasks/${this.data.taskId}/complete`,
        method: 'POST'
      })
      
      app.showToast('任务已完成', 'success')
      this.loadTaskDetail(this.data.taskId)
      
    } catch (error) {
      console.error('完成任务失败:', error)
      app.showToast('操作失败，请重试')
    } finally {
      app.hideLoading()
    }
  },

  // 采集样品
  async collectSample(e) {
    const sampleId = e.currentTarget.dataset.id
    
    try {
      await app.request({
        url: `/api/sampling/samples/${sampleId}/collect`,
        method: 'POST'
      })
      
      app.showToast('样品已采集', 'success')
      this.loadTaskDetail(this.data.taskId)
      
    } catch (error) {
      console.error('采集样品失败:', error)
      app.showToast('操作失败，请重试')
    }
  },

  // 提交样品
  async submitSample(e) {
    const sampleId = e.currentTarget.dataset.id
    
    try {
      await app.request({
        url: `/api/sampling/samples/${sampleId}/submit`,
        method: 'POST'
      })
      
      app.showToast('样品已提交', 'success')
      this.loadTaskDetail(this.data.taskId)
      
    } catch (error) {
      console.error('提交样品失败:', error)
      app.showToast('操作失败，请重试')
    }
  },

  // 查看样品详情
  viewSampleDetail(e) {
    const sampleId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/sampling/sample-detail?id=${sampleId}`
    })
  },

  // 添加样品
  addSample() {
    wx.navigateTo({
      url: `/pages/sampling/add-sample?taskId=${this.data.taskId}`
    })
  },

  // 扫码操作
  scanCode() {
    wx.scanCode({
      success: (res) => {
        console.log('扫码结果:', res)
        this.handleScanResult(res.result)
      },
      fail: (error) => {
        console.error('扫码失败:', error)
        app.showToast('扫码失败')
      }
    })
  },

  // 处理扫码结果
  handleScanResult(result) {
    try {
      const data = JSON.parse(result)
      
      if (data.type === 'sample') {
        // 样品二维码
        wx.navigateTo({
          url: `/pages/sampling/sample-detail?id=${data.id}`
        })
      } else if (data.type === 'equipment') {
        // 设备二维码
        wx.navigateTo({
          url: `/pages/equipment/detail?id=${data.id}`
        })
      } else {
        app.showToast('未识别的二维码类型')
      }
    } catch (error) {
      app.showToast(`扫码结果: ${result}`)
    }
  },

  // 分享功能
  onShareAppMessage() {
    return {
      title: `任务详情 - ${this.data.task.title}`,
      path: `/pages/sampling/task-detail?id=${this.data.taskId}`,
      imageUrl: '/static/images/task-share.png'
    }
  }
})
