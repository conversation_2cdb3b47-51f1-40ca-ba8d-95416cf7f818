/* pages/equipment/detail.wxss */

/* 设备基本信息 */
.equipment-info {
  margin-bottom: 20rpx;
}

.equipment-header {
  margin-bottom: 30rpx;
}

.equipment-name {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.name {
  font-size: 32rpx;
  font-weight: bold;
  color: #303133;
  flex: 1;
}

.status {
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: bold;
}

.status-online {
  background: #f0f9f0;
  color: #67C23A;
}

.status-offline {
  background: #fef0f0;
  color: #F56C6C;
}

.status-maintenance {
  background: #fdf6ec;
  color: #E6A23C;
}

.status-error {
  background: #fef0f0;
  color: #F56C6C;
}

.equipment-code {
  font-size: 24rpx;
  color: #909399;
}

.equipment-details {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.label {
  font-size: 26rpx;
  color: #606266;
  width: 160rpx;
}

.value {
  font-size: 26rpx;
  color: #303133;
  flex: 1;
  text-align: right;
}

/* 设备状态 */
.equipment-status {
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #303133;
  margin-bottom: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.more-text {
  font-size: 24rpx;
  color: #409EFF;
  font-weight: normal;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.status-item {
  text-align: center;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.status-label {
  display: block;
  font-size: 22rpx;
  color: #909399;
  margin-bottom: 8rpx;
}

.status-value {
  font-size: 28rpx;
  font-weight: bold;
  color: #303133;
}

.status-value.status-normal {
  color: #67C23A;
}

.status-value.status-warning {
  color: #E6A23C;
}

.status-value.status-error {
  color: #F56C6C;
}

/* 快捷操作 */
.quick-actions {
  margin-bottom: 20rpx;
}

.action-buttons {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 20rpx;
  background: #f8f9fa;
  border: none;
  border-radius: 16rpx;
  font-size: 24rpx;
  color: #606266;
  transition: all 0.2s;
}

.action-btn:active {
  transform: scale(0.95);
}

.action-btn image {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 12rpx;
}

.scan-btn {
  background: linear-gradient(135deg, #409EFF, #67C23A);
  color: white;
}

.check-btn {
  background: linear-gradient(135deg, #67C23A, #409EFF);
  color: white;
}

.maintain-btn {
  background: linear-gradient(135deg, #E6A23C, #F56C6C);
  color: white;
}

.report-btn {
  background: linear-gradient(135deg, #F56C6C, #E6A23C);
  color: white;
}

/* 维护记录 */
.maintenance-records {
  margin-bottom: 20rpx;
}

.record-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.record-item {
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border-left: 4rpx solid #409EFF;
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.record-type {
  font-size: 24rpx;
  font-weight: bold;
  color: #409EFF;
}

.record-date {
  font-size: 22rpx;
  color: #909399;
}

.record-content {
  display: block;
  font-size: 26rpx;
  color: #303133;
  line-height: 1.5;
  margin-bottom: 8rpx;
}

.record-operator {
  font-size: 22rpx;
  color: #909399;
}

/* 空状态 */
.empty-records {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 20rpx;
  text-align: center;
}

.empty-records image {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 20rpx;
  opacity: 0.6;
}

.empty-records text {
  font-size: 26rpx;
  color: #909399;
}

/* 加载状态 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-content {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #409EFF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 24rpx;
  color: #606266;
}
