<!--pages/equipment/detail.wxml-->
<view class="container">
  <!-- 设备基本信息 -->
  <view class="equipment-info card">
    <view class="equipment-header">
      <view class="equipment-name">
        <text class="name">{{equipment.name || '设备名称'}}</text>
        <view class="status status-{{equipment.status || 'online'}}">
          <text>{{equipment.statusText || '在线'}}</text>
        </view>
      </view>
      <text class="equipment-code">设备编号: {{equipment.code || 'EQ001'}}</text>
    </view>
    
    <view class="equipment-details">
      <view class="detail-row">
        <text class="label">设备型号:</text>
        <text class="value">{{equipment.model || 'Model-X1'}}</text>
      </view>
      <view class="detail-row">
        <text class="label">所在位置:</text>
        <text class="value">{{equipment.location || '实验室A区'}}</text>
      </view>
      <view class="detail-row">
        <text class="label">负责人:</text>
        <text class="value">{{equipment.manager || '张三'}}</text>
      </view>
      <view class="detail-row">
        <text class="label">最后检查:</text>
        <text class="value">{{equipment.lastCheck || '2024-01-15 10:30'}}</text>
      </view>
    </view>
  </view>

  <!-- 设备状态 -->
  <view class="equipment-status card">
    <view class="section-title">
      <text>设备状态</text>
    </view>
    <view class="status-grid">
      <view class="status-item">
        <text class="status-label">运行状态</text>
        <text class="status-value status-{{equipment.runStatus || 'normal'}}">
          {{equipment.runStatusText || '正常'}}
        </text>
      </view>
      <view class="status-item">
        <text class="status-label">温度</text>
        <text class="status-value">{{equipment.temperature || '25'}}°C</text>
      </view>
      <view class="status-item">
        <text class="status-label">湿度</text>
        <text class="status-value">{{equipment.humidity || '60'}}%</text>
      </view>
      <view class="status-item">
        <text class="status-label">使用时长</text>
        <text class="status-value">{{equipment.usageTime || '120'}}小时</text>
      </view>
    </view>
  </view>

  <!-- 快捷操作 -->
  <view class="quick-actions card">
    <view class="section-title">
      <text>快捷操作</text>
    </view>
    <view class="action-buttons">
      <button class="action-btn scan-btn" bindtap="scanEquipment">
        <image src="/static/icons/scan.png" mode="aspectFit"></image>
        <text>扫码识别</text>
      </button>
      <button class="action-btn check-btn" bindtap="checkEquipment">
        <image src="/static/icons/check.png" mode="aspectFit"></image>
        <text>设备检查</text>
      </button>
      <button class="action-btn maintain-btn" bindtap="maintainEquipment">
        <image src="/static/icons/maintain.png" mode="aspectFit"></image>
        <text>维护记录</text>
      </button>
      <button class="action-btn report-btn" bindtap="reportIssue">
        <image src="/static/icons/report.png" mode="aspectFit"></image>
        <text>故障报告</text>
      </button>
    </view>
  </view>

  <!-- 维护记录 -->
  <view class="maintenance-records card">
    <view class="section-title">
      <text>维护记录</text>
      <text class="more-text" bindtap="viewAllRecords">查看全部</text>
    </view>
    <view class="record-list">
      <view wx:for="{{maintenanceRecords}}" wx:key="id" class="record-item">
        <view class="record-header">
          <text class="record-type">{{item.type}}</text>
          <text class="record-date">{{item.date}}</text>
        </view>
        <text class="record-content">{{item.content}}</text>
        <text class="record-operator">操作人: {{item.operator}}</text>
      </view>
      
      <!-- 空状态 -->
      <view wx:if="{{maintenanceRecords.length === 0}}" class="empty-records">
        <image src="/static/images/empty-record.png" mode="aspectFit"></image>
        <text>暂无维护记录</text>
      </view>
    </view>
  </view>
</view>

<!-- 加载状态 -->
<view wx:if="{{loading}}" class="loading-overlay">
  <view class="loading-content">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>
</view>
