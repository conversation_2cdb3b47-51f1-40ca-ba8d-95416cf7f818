/* pages/printer/config.wxss */
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: 30rpx;
}

.page-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.page-subtitle {
  display: block;
  font-size: 28rpx;
  color: #666;
}

/* 卡片样式 */
.card {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* 连接状态 */
.connection-status {
  border-left: 8rpx solid #1890ff;
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.status-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.status-indicator {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.status-indicator.connected {
  background: #f6ffed;
  color: #52c41a;
  border: 2rpx solid #b7eb8f;
}

.status-indicator.disconnected {
  background: #fff2f0;
  color: #ff4d4f;
  border: 2rpx solid #ffccc7;
}

.connected-device {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.device-info {
  flex: 1;
}

.device-name {
  display: block;
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.device-id {
  display: block;
  font-size: 24rpx;
  color: #666;
}

.device-actions {
  display: flex;
  gap: 20rpx;
}

.no-connection {
  text-align: center;
  padding: 40rpx 0;
}

.no-connection-text {
  font-size: 28rpx;
  color: #999;
}

/* 搜索区域 */
.search-section {
  border-left: 8rpx solid #52c41a;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.search-tips {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.tips-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

/* 设备列表 */
.device-list {
  border-left: 8rpx solid #fa8c16;
}

.debug-tip {
  font-size: 24rpx;
  color: #666;
  font-style: italic;
}

.device-items {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.device-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background: #fafafa;
  border-radius: 12rpx;
  border: 2rpx solid #e8e8e8;
  transition: all 0.3s ease;
}

.device-item:active {
  background: #e6f7ff;
  border-color: #1890ff;
}



.device-main {
  display: flex;
  align-items: center;
  flex: 1;
}

.device-icon {
  width: 60rpx;
  height: 60rpx;
  margin-right: 20rpx;
}

.device-icon image {
  width: 100%;
  height: 100%;
}



.device-details {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.device-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.device-id {
  font-size: 24rpx;
  color: #666;
}

.device-rssi {
  font-size: 22rpx;
  color: #999;
}

.device-type {
  font-size: 20rpx;
  color: #722ed1;
  background: #f9f0ff;
  padding: 2rpx 8rpx;
  border-radius: 4rpx;
  margin-top: 4rpx;
  display: inline-block;
}

.device-adv {
  font-size: 20rpx;
  color: #52c41a;
  background: #f6ffed;
  padding: 2rpx 8rpx;
  border-radius: 4rpx;
  margin-top: 4rpx;
  display: inline-block;
}

.device-connect {
  padding: 8rpx 16rpx;
  background: #1890ff;
  border-radius: 20rpx;
}

.connect-text {
  font-size: 24rpx;
  color: white;
}



/* 打印设置 */
.print-settings {
  border-left: 8rpx solid #722ed1;
}

.settings-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 2rpx solid #f0f0f0;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-label {
  font-size: 30rpx;
  color: #333;
}

.setting-value {
  font-size: 28rpx;
  color: #1890ff;
  padding: 8rpx 16rpx;
  background: #f0f8ff;
  border-radius: 8rpx;
}

.setting-value.readonly {
  color: #666;
  background: #f5f5f5;
  font-style: italic;
}

/* 按钮样式 */
.btn-primary {
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 16rpx 32rpx;
  font-size: 28rpx;
}

.btn-primary.loading {
  background: #91d5ff;
}

.btn-secondary {
  background: #f0f0f0;
  color: #333;
  border: 2rpx solid #d9d9d9;
  border-radius: 8rpx;
  padding: 16rpx 32rpx;
  font-size: 28rpx;
}

.btn-danger {
  background: #ff4d4f;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 16rpx 32rpx;
  font-size: 28rpx;
}



.btn-info {
  background: #13c2c2;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 16rpx 32rpx;
  font-size: 28rpx;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  justify-content: center;
  gap: 20rpx;
  margin: 30rpx 0;
  flex-wrap: wrap;
}

/* 帮助说明 */
.help-section {
  border-left: 8rpx solid #13c2c2;
}

.help-content {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.help-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  padding-left: 20rpx;
  position: relative;
}

.help-text::before {
  content: '•';
  position: absolute;
  left: 0;
  color: #13c2c2;
  font-weight: bold;
}
