/* pages/printer/preview.wxss */
.preview-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

/* 顶部导航 */
.preview-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #e5e5e5;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-left {
  display: flex;
  align-items: center;
  color: #409EFF;
  font-size: 32rpx;
}

.back-icon {
  font-size: 40rpx;
  margin-right: 10rpx;
  font-weight: bold;
}

.back-text {
  font-size: 28rpx;
}

.header-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

.header-right {
  width: 120rpx;
}

/* 预览内容 */
.preview-content {
  flex: 1;
  padding: 30rpx;
  overflow-y: auto;
}

.preview-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
  text-align: center;
  margin-bottom: 10rpx;
}

.preview-subtitle {
  font-size: 24rpx;
  color: #666666;
  text-align: center;
  margin-bottom: 40rpx;
}

/* Canvas容器 */
.canvas-container {
  display: flex;
  justify-content: center;
  margin-bottom: 40rpx;
  padding: 30rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.preview-canvas {
  border: 2rpx solid #409EFF;
  border-radius: 8rpx;
  background-color: #ffffff;
}

/* 标签详情 */
.label-details {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.details-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.detail-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 24rpx;
  line-height: 1.5;
}

.detail-row:last-child {
  margin-bottom: 0;
}

.detail-label {
  font-size: 28rpx;
  color: #666666;
  min-width: 160rpx;
  flex-shrink: 0;
}

.detail-value {
  font-size: 28rpx;
  color: #333333;
  flex: 1;
  word-break: break-all;
}

/* 底部操作区 */
.preview-footer {
  background-color: #ffffff;
  padding: 30rpx;
  border-top: 1rpx solid #e5e5e5;
}

.footer-buttons {
  display: flex;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.btn {
  flex: 1;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-secondary {
  background-color: #f5f5f5;
  color: #666666;
}

.btn-secondary:active {
  background-color: #e5e5e5;
}

.btn-primary {
  background-color: #409EFF;
  color: #ffffff;
}

.btn-primary:active {
  background-color: #337ecc;
}

/* 打印机状态 */
.printer-status {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx;
  border-radius: 12rpx;
  background-color: #f0f9ff;
  border: 1rpx solid #409EFF;
}

.printer-status.no-printer {
  background-color: #fef2f2;
  border-color: #f56565;
}

.status-icon {
  font-size: 28rpx;
  margin-right: 12rpx;
}

.status-text {
  font-size: 24rpx;
  color: #409EFF;
}

.no-printer .status-text {
  color: #f56565;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .preview-content {
    padding: 20rpx;
  }
  
  .canvas-container {
    padding: 20rpx;
  }
  
  .label-details {
    padding: 20rpx;
  }
  
  .detail-label {
    min-width: 140rpx;
    font-size: 26rpx;
  }
  
  .detail-value {
    font-size: 26rpx;
  }
}
