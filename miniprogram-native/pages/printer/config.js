// pages/printer/config.js
const app = getApp()
const { TextEncoder, TextDecoder } = require('../../utils/text-encoding.js')

Page({
  data: {
    isScanning: false,
    allDevices: [], // 显示所有发现的设备
    connectedDevice: null,
    printerSettings: {
      paperWidth: 75,
      paperHeight: 60,
      printDensity: 'medium',
      printSpeed: 'normal',
      commandSet: 'CPCL' // 默认使用CPCL指令集
    }
  },

  onLoad() {
    console.log('打印机配置页面加载')
    this.loadSavedSettings()
  },

  onShow() {
    this.checkBluetoothStatus()
  },

  // 加载已保存的设置
  loadSavedSettings() {
    try {
      const savedDevice = wx.getStorageSync('connectedDevice')
      const savedSettings = wx.getStorageSync('printerSettings')
      
      if (savedDevice) {
        this.setData({ connectedDevice: savedDevice })
      }
      
      if (savedSettings) {
        this.setData({ printerSettings: savedSettings })
      }
    } catch (error) {
      console.error('加载打印机设置失败:', error)
    }
  },

  // 检查蓝牙状态
  async checkBluetoothStatus() {
    try {
      const bluetoothState = await wx.getBluetoothAdapterState()
      console.log('蓝牙状态:', bluetoothState)
      
      if (!bluetoothState.available) {
        wx.showModal({
          title: '蓝牙不可用',
          content: '请检查设备是否支持蓝牙功能',
          showCancel: false
        })
      }
    } catch (error) {
      console.log('蓝牙适配器未初始化')
    }
  },

  // 搜索蓝牙设备
  async searchBluetoothDevices() {
    if (this.data.isScanning) {
      return
    }

    try {
      this.setData({ isScanning: true, bluetoothDevices: [] })
      wx.showLoading({ title: '搜索BLE打印机...' })

      // 初始化蓝牙适配器
      await wx.openBluetoothAdapter()

      // 检查蓝牙状态
      const adapterState = await wx.getBluetoothAdapterState()
      console.log('蓝牙适配器状态:', adapterState)

      if (!adapterState.available) {
        throw new Error('蓝牙不可用')
      }

      if (!adapterState.discovering) {
        // 开始搜索BLE设备 - 针对低功耗蓝牙优化
        await wx.startBluetoothDevicesDiscovery({
          services: [], // 不指定服务UUID，搜索所有设备
          allowDuplicatesKey: true, // 允许重复设备，获取最新RSSI
          interval: 0, // 立即上报
          powerLevel: 'high' // 使用高功率搜索
        })
      }

      // 监听设备发现
      wx.onBluetoothDeviceFound((res) => {
        console.log('发现蓝牙设备:', res.devices)

        // 保存所有设备
        const currentAllDevices = this.data.allDevices
        res.devices.forEach(device => {
          const existingIndex = currentAllDevices.findIndex(existing =>
            existing.deviceId === device.deviceId
          )
          if (existingIndex >= 0) {
            currentAllDevices[existingIndex] = { ...currentAllDevices[existingIndex], ...device }
          } else {
            currentAllDevices.push({
              ...device,
              displayName: device.name || `BLE设备 (${device.deviceId.slice(-4)})`
            })
          }
        })

        this.setData({
          allDevices: [...currentAllDevices]
        })
      })

      // 延长搜索时间到10秒，BLE设备可能需要更长时间
      setTimeout(() => {
        this.stopBluetoothSearch()
      }, 10000)

    } catch (error) {
      wx.hideLoading()
      this.setData({ isScanning: false })
      
      console.error('搜索蓝牙设备失败:', error)
      
      if (error.errCode === 10001) {
        wx.showModal({
          title: '蓝牙未开启',
          content: '请先开启手机蓝牙功能',
          showCancel: false
        })
      } else {
        wx.showModal({
          title: '搜索失败',
          content: '搜索蓝牙设备失败，请重试',
          showCancel: false
        })
      }
    }
  },

  // 停止蓝牙搜索
  async stopBluetoothSearch() {
    try {
      await wx.stopBluetoothDevicesDiscovery()
      wx.hideLoading()
      this.setData({ isScanning: false })
      
      if (this.data.bluetoothDevices.length === 0) {
        wx.showToast({
          title: '未发现打印机',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('停止蓝牙搜索失败:', error)
      wx.hideLoading()
      this.setData({ isScanning: false })
    }
  },

  // 连接打印机
  async connectPrinter(e) {
    const device = e.currentTarget.dataset.device

    // 如果是无名称设备，先确认
    if (!device.name) {
      const confirmResult = await new Promise(resolve => {
        wx.showModal({
          title: '确认连接',
          content: `确定要连接这个设备吗？\n\n设备ID: ${device.deviceId}\n信号强度: ${device.RSSI}dBm\n\n这可能是您的BLE打印机。`,
          confirmText: '尝试连接',
          cancelText: '取消',
          success: (res) => resolve(res.confirm)
        })
      })

      if (!confirmResult) {
        return
      }
    }

    try {
      wx.showLoading({ title: '连接中...' })
      console.log('尝试连接设备:', device)

      // 停止搜索以释放资源
      try {
        await wx.stopBluetoothDevicesDiscovery()
      } catch (e) {
        console.log('停止搜索失败:', e)
      }

      // 创建BLE连接
      await wx.createBLEConnection({
        deviceId: device.deviceId,
        timeout: 10000 // 10秒超时
      })

      console.log('BLE连接创建成功')

      // 等待连接稳定
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 获取设备服务
      const servicesResult = await wx.getBLEDeviceServices({
        deviceId: device.deviceId
      })

      console.log('设备服务列表:', servicesResult.services)

      // 查找打印服务和特征
      let printService = null
      let writeCharacteristic = null

      for (const service of servicesResult.services) {
        try {
          const characteristicsResult = await wx.getBLEDeviceCharacteristics({
            deviceId: device.deviceId,
            serviceId: service.uuid
          })

          console.log(`服务 ${service.uuid} 的特征:`, characteristicsResult.characteristics)

          // 查找可写特征
          const writableChar = characteristicsResult.characteristics.find(char =>
            char.properties.write || char.properties.writeNoResponse
          )

          if (writableChar) {
            printService = service
            writeCharacteristic = writableChar
            console.log('找到打印服务:', service.uuid, '特征:', writableChar.uuid)
            break
          }
        } catch (charError) {
          console.log('获取特征失败:', charError)
        }
      }

      // 保存连接信息
      const deviceInfo = {
        ...device,
        serviceId: printService?.uuid,
        characteristicId: writeCharacteristic?.uuid,
        connectedAt: new Date().toISOString()
      }

      this.setData({ connectedDevice: deviceInfo })
      wx.setStorageSync('connectedDevice', deviceInfo)

      wx.hideLoading()
      wx.showToast({
        title: '连接成功',
        icon: 'success'
      })

      // 监听连接状态变化
      wx.onBLEConnectionStateChange((res) => {
        console.log('BLE连接状态变化:', res)
        if (!res.connected && this.data.connectedDevice?.deviceId === res.deviceId) {
          this.setData({ connectedDevice: null })
          wx.removeStorageSync('connectedDevice')
          wx.showToast({
            title: '设备已断开',
            icon: 'none'
          })
        }
      })

    } catch (error) {
      wx.hideLoading()
      console.error('连接打印机失败:', error)

      let errorMsg = '连接失败'
      if (error.errCode === 10003) {
        errorMsg = '连接失败，请确保设备未被其他应用连接'
      } else if (error.errCode === 10012) {
        errorMsg = '连接超时，请重试'
      } else if (error.errMsg) {
        errorMsg = error.errMsg
      }

      wx.showModal({
        title: '连接失败',
        content: errorMsg,
        showCancel: false
      })
    }
  },

  // 断开打印机连接
  async disconnectPrinter() {
    if (!this.data.connectedDevice) {
      return
    }

    try {
      await wx.closeBLEConnection({
        deviceId: this.data.connectedDevice.deviceId
      })
      
      this.setData({ connectedDevice: null })
      wx.removeStorageSync('connectedDevice')
      
      wx.showToast({
        title: '已断开连接',
        icon: 'success'
      })
    } catch (error) {
      console.error('断开连接失败:', error)
      wx.showToast({
        title: '断开失败',
        icon: 'error'
      })
    }
  },

  // 测试打印
  async testPrint() {
    if (!this.data.connectedDevice) {
      wx.showToast({
        title: '请先连接打印机',
        icon: 'none'
      })
      return
    }

    if (!this.data.connectedDevice.serviceId || !this.data.connectedDevice.characteristicId) {
      wx.showToast({
        title: '设备服务未就绪，请重新连接',
        icon: 'none'
      })
      return
    }

    // 显示测试选项
    wx.showActionSheet({
      itemList: ['基础CPCL测试', '完整CPCL测试', '中文编码测试', '字体测试', 'ESC/POS测试', 'ESC/POS中文测试'],
      success: async (res) => {
        try {
          wx.showLoading({ title: '测试打印中...' })
          console.log('开始测试打印，类型:', res.tapIndex)

          let testCommands
          if (res.tapIndex === 0) {
            // 基础CPCL测试
            testCommands = this.generateBasicTestCommands()
          } else if (res.tapIndex === 1) {
            // 完整CPCL测试
            testCommands = this.generateTestPrintCommands()
          } else if (res.tapIndex === 2) {
            // 中文编码测试
            testCommands = this.generateChineseTestCommands()
          } else if (res.tapIndex === 3) {
            // 字体测试
            testCommands = this.generateFontTestCommands()
          } else if (res.tapIndex === 4) {
            // ESC/POS测试
            testCommands = this.generateESCPOSTestCommands()
          } else {
            // ESC/POS中文测试
            testCommands = this.generateESCPOSChineseTestCommands()
          }

          // 发送打印数据
          await this.sendBLEData(this.data.connectedDevice, testCommands)

          wx.hideLoading()
          wx.showToast({
            title: '测试指令已发送',
            icon: 'success'
          })

        } catch (error) {
          wx.hideLoading()
          console.error('测试打印失败:', error)

          let errorMsg = '测试打印失败'
          if (error.errCode === 10006) {
            errorMsg = '设备连接已断开，请重新连接'
          } else if (error.errCode === 10007) {
            errorMsg = '特征不支持写入操作'
          }

          wx.showModal({
            title: '打印失败',
            content: errorMsg + '\n\n错误详情: ' + (error.errMsg || error.message),
            showCancel: false
          })
        }
      }
    })
  },

  // 生成基础测试指令
  generateBasicTestCommands() {
    console.log('生成基础CPCL测试指令')

    const commands = []

    // 最简单的CPCL指令
    commands.push(...this.stringToBytes('! 0 200 200 200 1\r\n'))
    commands.push(...this.stringToBytes('TEXT 1 0 10 10 Basic Test\r\n'))
    commands.push(...this.stringToBytes('TEXT 1 0 10 30 CPCL Printer\r\n'))
    commands.push(...this.stringToBytes('TEXT 1 0 10 50 Font Size Test\r\n'))
    commands.push(...this.stringToBytes('PRINT\r\n'))

    return new Uint8Array(commands)
  },

  // 生成测试打印指令
  generateTestPrintCommands() {
    const device = this.data.connectedDevice
    const settings = this.data.printerSettings

    // 根据设置选择指令集
    if (settings.commandSet === 'CPCL') {
      return this.generateCPCLCommands(device, settings)
    } else {
      return this.generateESCPOSCommands(device, settings)
    }
  },

  // 生成ESC/POS指令
  generateESCPOSCommands(device, settings) {
    console.log('生成ESC/POS指令')

    const ESC = 0x1B
    const GS = 0x1D
    const commands = []

    // 初始化打印机
    commands.push(ESC, 0x40)

    // 设置字符编码为GB2312/GBK（中文支持）
    commands.push(ESC, 0x74, 0x01)

    // 设置打印密度和速度
    const density = settings.printDensity === 'light' ? 1 :
                   settings.printDensity === 'medium' ? 8 : 15
    commands.push(GS, 0x7C, density)

    // 设置行间距
    commands.push(ESC, 0x33, 30)

    // 居中对齐
    commands.push(ESC, 0x61, 0x01)

    // 标题 - 加粗
    commands.push(ESC, 0x45, 0x01)
    commands.push(...this.stringToGBK('=== 打印测试 ==='))
    commands.push(0x0A, 0x0A)
    commands.push(ESC, 0x45, 0x00)

    // 左对齐
    commands.push(ESC, 0x61, 0x00)

    // 设备信息
    const deviceName = `设备: ${device.displayName || device.name || '未知设备'}`
    commands.push(...this.stringToGBK(deviceName))
    commands.push(0x0A)

    // 时间信息
    const timeStr = `时间: ${new Date().toLocaleString()}`
    commands.push(...this.stringToGBK(timeStr))
    commands.push(0x0A)

    // 设置信息
    const sizeStr = `纸张: ${settings.paperWidth}x${settings.paperHeight}mm`
    commands.push(...this.stringToGBK(sizeStr))
    commands.push(0x0A)

    // 分割线
    commands.push(...this.stringToGBK('------------------------'))
    commands.push(0x0A)

    // 测试文本
    commands.push(...this.stringToGBK('LIMS系统打印测试成功!'))
    commands.push(0x0A, 0x0A, 0x0A)

    // 走纸
    commands.push(ESC, 0x64, 3) // 走纸3行

    // 切纸指令（如果支持）
    commands.push(GS, 0x56, 0x00)

    return new Uint8Array(commands)
  },

  // 生成CPCL指令
  generateCPCLCommands(device, settings) {
    console.log('生成CPCL指令')

    const commands = []

    // CPCL标签开始 - 使用较小的DPI和尺寸
    const labelHeight = Math.min(settings.paperHeight * 6, 300) // 限制高度
    const labelStart = `! 0 200 200 ${labelHeight} 1\r\n`
    commands.push(...this.stringToBytes(labelStart))

    // 使用纯ASCII内容进行测试
    commands.push(...this.stringToBytes('TEXT 3 0 10 10 === Print Test ===\r\n'))

    // 设备信息 - 纯英文
    const deviceName = device.displayName || device.name || 'Unknown Device'
    commands.push(...this.stringToBytes(`TEXT 3 0 10 30 Device: ${deviceName}\r\n`))

    // 时间信息 - 纯英文
    const now = new Date()
    const timeStr = `${now.getFullYear()}-${(now.getMonth()+1).toString().padStart(2,'0')}-${now.getDate().toString().padStart(2,'0')} ${now.getHours().toString().padStart(2,'0')}:${now.getMinutes().toString().padStart(2,'0')}`
    commands.push(...this.stringToBytes(`TEXT 3 0 10 50 Time: ${timeStr}\r\n`))

    // 纸张信息 - 纯英文
    commands.push(...this.stringToBytes(`TEXT 3 0 10 70 Paper: ${settings.paperWidth}x${settings.paperHeight}mm\r\n`))

    // 分割线
    commands.push(...this.stringToBytes('TEXT 3 0 10 90 ------------------------\r\n'))

    // 测试文本 - 纯英文
    commands.push(...this.stringToBytes('TEXT 3 0 10 110 LIMS Print Test OK!\r\n'))

    // 添加GB2312编码声明（测试证明有效）
    commands.push(...this.stringToBytes('ENCODING GB2312\r\n'))

    // 中文测试 - 使用GBK编码
    commands.push(...this.stringToBytes('TEXT 3 0 10 130 '))
    const gbkEncoder = new TextEncoder('gbk')
    const chineseBytes = Array.from(gbkEncoder.encode('LIMS系统打印测试成功！'))
    commands.push(...chineseBytes)
    commands.push(...this.stringToBytes('\r\n'))

    // 打印标签
    commands.push(...this.stringToBytes('PRINT\r\n'))

    return new Uint8Array(commands)
  },

  // 生成中文编码测试指令
  generateChineseTestCommands() {
    console.log('生成中文编码测试指令')

    const commands = []

    // CPCL标签开始
    commands.push(...this.stringToBytes('! 0 200 200 300 1\r\n'))

    // 测试1: 使用TextEncoder UTF-8编码
    commands.push(...this.stringToBytes('TEXT 3 0 10 10 UTF8: '))
    const utf8Encoder = new TextEncoder('utf-8')
    const utf8Bytes = Array.from(utf8Encoder.encode('测试'))
    commands.push(...utf8Bytes)
    commands.push(...this.stringToBytes('\r\n'))

    // 测试2: 使用TextEncoder GBK编码
    commands.push(...this.stringToBytes('TEXT 3 0 10 40 GBK: '))
    const gbkEncoder = new TextEncoder('gbk')
    const gbkBytes = Array.from(gbkEncoder.encode('测试'))
    commands.push(...gbkBytes)
    commands.push(...this.stringToBytes('\r\n'))

    // 测试3: 使用GB2312编码声明
    commands.push(...this.stringToBytes('ENCODING GB2312\r\n'))
    commands.push(...this.stringToBytes('TEXT 3 0 10 70 GB2312: '))
    commands.push(...gbkBytes)
    commands.push(...this.stringToBytes('\r\n'))

    // 测试4: 纯ASCII
    commands.push(...this.stringToBytes('TEXT 3 0 10 100 ASCII: Test OK\r\n'))

    // 测试5: 十六进制显示
    commands.push(...this.stringToBytes('TEXT 1 0 10 130 UTF8-HEX: '))
    utf8Bytes.forEach(byte => {
      commands.push(...this.stringToBytes(byte.toString(16).padStart(2, '0').toUpperCase() + ' '))
    })
    commands.push(...this.stringToBytes('\r\n'))

    commands.push(...this.stringToBytes('TEXT 1 0 10 160 GBK-HEX: '))
    gbkBytes.forEach(byte => {
      commands.push(...this.stringToBytes(byte.toString(16).padStart(2, '0').toUpperCase() + ' '))
    })
    commands.push(...this.stringToBytes('\r\n'))

    // 打印标签
    commands.push(...this.stringToBytes('PRINT\r\n'))

    return new Uint8Array(commands)
  },

  // 生成字体测试指令
  generateFontTestCommands() {
    console.log('生成字体测试指令')

    const commands = []

    // CPCL标签开始
    commands.push(...this.stringToBytes('! 0 200 200 400 1\r\n'))

    // 测试不同字体ID (0-7)
    const testText = 'Font Test'
    const chineseBytes = [0xE6, 0xB5, 0x8B, 0xE8, 0xAF, 0x95] // "测试"

    let yPos = 10
    for (let fontId = 0; fontId <= 7; fontId++) {
      // 英文测试
      commands.push(...this.stringToBytes(`TEXT ${fontId} 0 10 ${yPos} Font${fontId}: ${testText}\r\n`))

      // 中文测试
      commands.push(...this.stringToBytes(`TEXT ${fontId} 0 200 ${yPos} `))
      commands.push(...chineseBytes)
      commands.push(...this.stringToBytes('\r\n'))

      yPos += 35
    }

    // 测试不同字体方向 (0=正常, 1=90度, 2=180度, 3=270度)
    yPos += 20
    commands.push(...this.stringToBytes(`TEXT 1 0 10 ${yPos} Rotation Test:\r\n`))
    yPos += 25

    for (let rotation = 0; rotation <= 3; rotation++) {
      commands.push(...this.stringToBytes(`TEXT 1 ${rotation} 10 ${yPos} R${rotation}: `))
      commands.push(...chineseBytes)
      commands.push(...this.stringToBytes('\r\n'))
      yPos += 25
    }

    // 打印标签
    commands.push(...this.stringToBytes('PRINT\r\n'))

    return new Uint8Array(commands)
  },

  // 生成ESC/POS测试指令
  generateESCPOSTestCommands() {
    console.log('生成ESC/POS测试指令')

    const commands = []

    // ESC/POS初始化
    commands.push(0x1B, 0x40) // ESC @ - 初始化打印机

    // 设置字符集为GB2312
    commands.push(0x1C, 0x43, 0x01) // FS C 1 - 选择GB2312字符集

    // 标题 - 居中对齐
    commands.push(0x1B, 0x61, 0x01) // ESC a 1 - 居中对齐
    commands.push(...this.stringToBytes('=== ESC/POS Test ===\n'))

    // 左对齐
    commands.push(0x1B, 0x61, 0x00) // ESC a 0 - 左对齐

    // 设备信息
    const deviceName = this.data.connectedDevice.displayName || this.data.connectedDevice.name || 'Unknown Device'
    commands.push(...this.stringToBytes(`Device: ${deviceName}\n`))

    // 时间信息
    const now = new Date()
    const timeStr = `${now.getFullYear()}-${(now.getMonth()+1).toString().padStart(2,'0')}-${now.getDate().toString().padStart(2,'0')} ${now.getHours().toString().padStart(2,'0')}:${now.getMinutes().toString().padStart(2,'0')}`
    commands.push(...this.stringToBytes(`Time: ${timeStr}\n`))

    // 分割线
    commands.push(...this.stringToBytes('------------------------\n'))

    // 字体大小测试
    commands.push(...this.stringToBytes('Font Size Test:\n'))

    // 正常字体
    commands.push(...this.stringToBytes('Normal: LIMS Print Test\n'))

    // 双倍宽度
    commands.push(0x1B, 0x21, 0x20) // ESC ! 32 - 双倍宽度
    commands.push(...this.stringToBytes('Wide: LIMS Test\n'))

    // 双倍高度
    commands.push(0x1B, 0x21, 0x10) // ESC ! 16 - 双倍高度
    commands.push(...this.stringToBytes('Tall: LIMS Test\n'))

    // 双倍宽高
    commands.push(0x1B, 0x21, 0x30) // ESC ! 48 - 双倍宽高
    commands.push(...this.stringToBytes('Big: LIMS\n'))

    // 恢复正常
    commands.push(0x1B, 0x21, 0x00) // ESC ! 0 - 正常字体

    // 加粗测试
    commands.push(0x1B, 0x45, 0x01) // ESC E 1 - 加粗开
    commands.push(...this.stringToBytes('Bold: LIMS Print Test\n'))
    commands.push(0x1B, 0x45, 0x00) // ESC E 0 - 加粗关

    // 下划线测试
    commands.push(0x1B, 0x2D, 0x01) // ESC - 1 - 下划线开
    commands.push(...this.stringToBytes('Underline: LIMS Test\n'))
    commands.push(0x1B, 0x2D, 0x00) // ESC - 0 - 下划线关

    // 走纸
    commands.push(...this.stringToBytes('\n\n\n'))

    // 切纸（如果支持）
    commands.push(0x1D, 0x56, 0x00) // GS V 0 - 全切

    return new Uint8Array(commands)
  },

  // 生成ESC/POS中文测试指令
  generateESCPOSChineseTestCommands() {
    console.log('生成ESC/POS中文测试指令')

    const commands = []

    // ESC/POS初始化
    commands.push(0x1B, 0x40) // ESC @ - 初始化打印机

    // 设置字符集
    commands.push(0x1C, 0x43, 0x01) // FS C 1 - 选择GB2312字符集

    // 标题
    commands.push(0x1B, 0x61, 0x01) // 居中对齐
    commands.push(...this.stringToBytes('=== Chinese Test ===\n'))
    commands.push(0x1B, 0x61, 0x00) // 左对齐

    // 测试1: 直接UTF-8编码
    commands.push(...this.stringToBytes('UTF8: '))
    const utf8Bytes = [0xE6, 0xB5, 0x8B, 0xE8, 0xAF, 0x95] // "测试"
    commands.push(...utf8Bytes)
    commands.push(...this.stringToBytes('\n'))

    // 测试2: GBK编码
    commands.push(...this.stringToBytes('GBK: '))
    const gbkBytes = [0xB2, 0xE2, 0xCA, 0xD4] // "测试"的GBK编码
    commands.push(...gbkBytes)
    commands.push(...this.stringToBytes('\n'))

    // 测试3: 设置中文模式
    commands.push(0x1C, 0x26) // FS & - 选择汉字模式
    commands.push(...this.stringToBytes('Chinese Mode: '))
    commands.push(...utf8Bytes)
    commands.push(...this.stringToBytes('\n'))
    commands.push(0x1C, 0x2E) // FS . - 取消汉字模式

    // 测试4: 不同字符集
    commands.push(0x1B, 0x74, 0x00) // ESC t 0 - PC437字符集
    commands.push(...this.stringToBytes('PC437: '))
    commands.push(...utf8Bytes)
    commands.push(...this.stringToBytes('\n'))

    commands.push(0x1B, 0x74, 0x01) // ESC t 1 - Katakana字符集
    commands.push(...this.stringToBytes('Katakana: '))
    commands.push(...utf8Bytes)
    commands.push(...this.stringToBytes('\n'))

    commands.push(0x1B, 0x74, 0x03) // ESC t 3 - PC850字符集
    commands.push(...this.stringToBytes('PC850: '))
    commands.push(...utf8Bytes)
    commands.push(...this.stringToBytes('\n'))

    // 测试5: 中文字体大小
    commands.push(...this.stringToBytes('Size Test:\n'))

    // 正常中文
    commands.push(...this.stringToBytes('Normal: '))
    commands.push(...utf8Bytes)
    commands.push(...this.stringToBytes('\n'))

    // 双倍宽度中文
    commands.push(0x1B, 0x21, 0x20) // 双倍宽度
    commands.push(...this.stringToBytes('Wide: '))
    commands.push(...utf8Bytes)
    commands.push(...this.stringToBytes('\n'))

    // 双倍高度中文
    commands.push(0x1B, 0x21, 0x10) // 双倍高度
    commands.push(...this.stringToBytes('Tall: '))
    commands.push(...utf8Bytes)
    commands.push(...this.stringToBytes('\n'))

    // 恢复正常
    commands.push(0x1B, 0x21, 0x00)

    // 十六进制显示
    commands.push(...this.stringToBytes('UTF8-HEX: '))
    utf8Bytes.forEach(byte => {
      commands.push(...this.stringToBytes(byte.toString(16).padStart(2, '0').toUpperCase() + ' '))
    })
    commands.push(...this.stringToBytes('\n'))

    commands.push(...this.stringToBytes('GBK-HEX: '))
    gbkBytes.forEach(byte => {
      commands.push(...this.stringToBytes(byte.toString(16).padStart(2, '0').toUpperCase() + ' '))
    })
    commands.push(...this.stringToBytes('\n'))

    // 走纸
    commands.push(...this.stringToBytes('\n\n\n'))

    return new Uint8Array(commands)
  },

  // 字符串转GBK编码（简化版）
  stringToGBK(str) {
    const bytes = []
    for (let i = 0; i < str.length; i++) {
      const char = str.charAt(i)
      const code = str.charCodeAt(i)

      if (code < 0x80) {
        // ASCII字符直接输出
        bytes.push(code)
      } else {
        // 中文字符处理
        const gbkBytes = this.chineseToGBK(char)
        if (gbkBytes.length > 0) {
          bytes.push(...gbkBytes)
        } else {
          // 如果无法转换，使用问号替代
          bytes.push(0x3F) // '?'
        }
      }
    }
    return bytes
  },

  // 简化的中文字符转GBK
  chineseToGBK(char) {
    // 常用中文字符的GBK编码映射（简化版）
    const commonChars = {
      '打': [0xB4, 0xF2],
      '印': [0xD3, 0xA1],
      '测': [0xB2, 0xE2],
      '试': [0xCA, 0xD4],
      '设': [0xC9, 0xE8],
      '备': [0xB1, 0xB8],
      '时': [0xCA, 0xB1],
      '间': [0xBC, 0xE4],
      '纸': [0xD6, 0xBD],
      '张': [0xD5, 0xC5],
      '系': [0xCF, 0xB5],
      '统': [0xCD, 0xB3],
      '成': [0xB3, 0xC9],
      '功': [0xB9, 0xA6],
      '样': [0xD1, 0xF9],
      '品': [0xC6, 0xB7],
      '类': [0xC0, 0xE0],
      '别': [0xB1, 0xF0],
      '编': [0xB1, 0xE0],
      '号': [0xBA, 0xC5],
      '采': [0xB2, 0xC9],
      '日': [0xC8, 0xD5],
      '期': [0xC6, 0xDA],
      '点': [0xB5, 0xE3],
      '位': [0xCE, 0xBB],
      '检': [0xBC, 0xEC],
      '项': [0xCF, 0xEE],
      '目': [0xC4, 0xBF],
      '保': [0xB1, 0xA3],
      '存': [0xB4, 0xE6],
      '容': [0xC8, 0xDD],
      '器': [0xC6, 0xF7],
      '方': [0xB7, 0xBD],
      '式': [0xCA, 0xBD]
    }

    if (commonChars[char]) {
      return commonChars[char]
    }

    // 如果不在常用字符中，尝试使用Unicode转换（简化处理）
    const code = char.charCodeAt(0)
    if (code >= 0x4E00 && code <= 0x9FFF) {
      // 中文字符范围，使用简化的双字节编码
      const high = Math.floor((code - 0x4E00) / 256) + 0xA1
      const low = ((code - 0x4E00) % 256) + 0xA1
      return [Math.min(high, 0xFE), Math.min(low, 0xFE)]
    }

    return [] // 无法转换
  },

  // 字符串转UTF-8编码
  stringToUTF8(str) {
    const bytes = []
    for (let i = 0; i < str.length; i++) {
      const code = str.charCodeAt(i)
      if (code < 0x80) {
        bytes.push(code)
      } else if (code < 0x800) {
        bytes.push(0xC0 | (code >> 6))
        bytes.push(0x80 | (code & 0x3F))
      } else {
        bytes.push(0xE0 | (code >> 12))
        bytes.push(0x80 | ((code >> 6) & 0x3F))
        bytes.push(0x80 | (code & 0x3F))
      }
    }
    return bytes
  },

  // 简单的中文字符转UTF-8（常用字符）
  chineseToUTF8(char) {
    const commonChars = {
      '测': [0xE6, 0xB5, 0x8B],
      '试': [0xE8, 0xAF, 0x95],
      '打': [0xE6, 0x89, 0x93],
      '印': [0xE5, 0x8D, 0xB0],
      '样': [0xE6, 0xA0, 0xB7],
      '品': [0xE5, 0x93, 0x81],
      '类': [0xE7, 0xB1, 0xBB],
      '别': [0xE5, 0x88, 0xAB],
      '编': [0xE7, 0xBC, 0x96],
      '号': [0xE5, 0x8F, 0xB7],
      '采': [0xE9, 0x87, 0x87],
      '日': [0xE6, 0x97, 0xA5],
      '期': [0xE6, 0x9C, 0x9F],
      '时': [0xE6, 0x97, 0xB6],
      '间': [0xE9, 0x97, 0xB4],
      '点': [0xE7, 0x82, 0xB9],
      '位': [0xE4, 0xBD, 0x8D],
      '检': [0xE6, 0xA3, 0x80],
      '项': [0xE9, 0xA1, 0xB9],
      '目': [0xE7, 0x9B, 0xAE],
      '保': [0xE4, 0xBF, 0x9D],
      '存': [0xE5, 0xAD, 0x98],
      '容': [0xE5, 0xAE, 0xB9],
      '器': [0xE5, 0x99, 0xA8],
      '方': [0xE6, 0x96, 0xB9],
      '式': [0xE5, 0xBC, 0x8F],
      '状': [0xE7, 0x8A, 0xB6],
      '态': [0xE6, 0x80, 0x81],
      '系': [0xE7, 0xB3, 0xBB],
      '统': [0xE7, 0xBB, 0x9F],
      '成': [0xE6, 0x88, 0x90],
      '功': [0xE5, 0x8A, 0x9F]
    }

    return commonChars[char] || []
  },

  // 字符串转字节数组
  stringToBytes(str) {
    const bytes = []
    for (let i = 0; i < str.length; i++) {
      const code = str.charCodeAt(i)
      if (code < 0x80) {
        bytes.push(code)
      } else if (code < 0x800) {
        bytes.push(0xC0 | (code >> 6))
        bytes.push(0x80 | (code & 0x3F))
      } else {
        bytes.push(0xE0 | (code >> 12))
        bytes.push(0x80 | ((code >> 6) & 0x3F))
        bytes.push(0x80 | (code & 0x3F))
      }
    }
    return bytes
  },

  // 发送BLE数据
  async sendBLEData(device, data) {
    const maxChunkSize = 20 // BLE每次最大传输20字节

    console.log('开始发送数据，总长度:', data.length, '字节')
    console.log('数据内容:', Array.from(data).map(b => '0x' + b.toString(16).padStart(2, '0')).join(' '))

    let sentBytes = 0

    for (let i = 0; i < data.length; i += maxChunkSize) {
      const chunk = data.slice(i, i + maxChunkSize)

      console.log(`发送第${Math.floor(i/maxChunkSize) + 1}块数据:`, Array.from(chunk).map(b => '0x' + b.toString(16).padStart(2, '0')).join(' '))

      try {
        await wx.writeBLECharacteristicValue({
          deviceId: device.deviceId,
          serviceId: device.serviceId,
          characteristicId: device.characteristicId,
          value: chunk.buffer
        })

        sentBytes += chunk.length
        console.log(`已发送 ${sentBytes}/${data.length} 字节`)

        // 每次发送后稍作延迟，避免数据丢失
        await new Promise(resolve => setTimeout(resolve, 100))

      } catch (error) {
        console.error(`发送第${Math.floor(i/maxChunkSize) + 1}块数据失败:`, error)
        throw error
      }
    }

    console.log('数据发送完成，总计:', sentBytes, '字节')
  },

  // 发送打印数据（示例）
  async sendPrintData(content) {
    // 这里需要根据具体打印机型号实现
    // 不同品牌的打印机有不同的指令集
    console.log('发送打印数据:', content)
    
    // 模拟发送延迟
    return new Promise(resolve => {
      setTimeout(resolve, 2000)
    })
  },

  // 修改打印设置
  onSettingChange(e) {
    const { field } = e.currentTarget.dataset
    const { value } = e.detail

    let newValue = value

    // 根据字段类型处理值
    if (field === 'paperWidth') {
      newValue = [58, 75, 80][value]
    } else if (field === 'paperHeight') {
      newValue = [40, 50, 60, 80, 100][value]
    } else if (field === 'printDensity') {
      newValue = ['light', 'medium', 'dark'][value]
    } else if (field === 'printSpeed') {
      newValue = ['slow', 'normal', 'fast'][value]
    } else if (field === 'commandSet') {
      newValue = ['ESC/POS', 'CPCL'][value]
    }

    this.setData({
      [`printerSettings.${field}`]: newValue
    })

    // 保存设置
    wx.setStorageSync('printerSettings', this.data.printerSettings)

    console.log('设置已更新:', field, newValue)
  },

  // 清除所有设置
  clearSettings() {
    wx.showModal({
      title: '确认清除',
      content: '确定要清除所有打印机设置吗？',
      success: (res) => {
        if (res.confirm) {
          wx.removeStorageSync('connectedDevice')
          wx.removeStorageSync('printerSettings')

          this.setData({
            connectedDevice: null,
            printerSettings: {
              paperWidth: 75,
              paperHeight: 60,
              printDensity: 'medium',
              printSpeed: 'normal',
              commandSet: 'CPCL'
            }
          })

          wx.showToast({
            title: '设置已清除',
            icon: 'success'
          })
        }
      }
    })
  },

  // 显示使用说明
  showUsageHelp() {
    wx.showModal({
      title: '使用说明',
      content: '1. 确保打印机已开启并处于配对模式\n2. 点击"搜索设备"按钮\n3. 在设备列表中选择您的打印机\n4. 连接成功后进行测试打印\n5. 根据需要调整打印设置',
      showCancel: false
    })
  }
})
