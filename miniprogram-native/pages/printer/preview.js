// pages/printer/preview.js
const app = getApp()
const { TextEncoder, TextDecoder } = require('../../utils/text-encoding.js')

Page({
  data: {
    // 标签数据
    labelData: null,
    taskInfo: null,
    bottleGroup: null,
    
    // 预览设置
    previewScale: 1,
    canvasWidth: 300,
    canvasHeight: 240,
    
    // 打印设置
    printerSettings: {
      paperWidth: 75,
      paperHeight: 60,
      commandSet: 'CPCL'
    },
    
    // 连接的打印机
    connectedDevice: null
  },

  onLoad(options) {
    console.log('打印预览页面加载', options)

    // 获取传递的数据
    if (options.data) {
      try {
        const data = JSON.parse(decodeURIComponent(options.data))
        console.log('解析的数据:', data)

        this.setData({
          taskInfo: data.taskInfo,
          bottleGroup: data.bottleGroup
        })

        // 生成标签数据
        this.generateLabelData()

        // 加载打印机设置
        this.loadPrinterSettings()

      } catch (error) {
        console.error('解析传递数据失败:', error)
        wx.showToast({
          title: '数据加载失败',
          icon: 'none'
        })
      }
    }
  },

  onReady() {
    console.log('页面准备完成')
    // 绘制将在generateLabelData完成后自动执行
  },

  // 生成标签数据
  generateLabelData() {
    const { taskInfo, bottleGroup } = this.data
    
    if (!taskInfo || !bottleGroup) return
    
    // 构建样品编号：任务编号（样品序号/样品总数量）
    const taskCode = taskInfo.taskCode || taskInfo.projectName || 'LIMS001'
    const sampleIndex = bottleGroup.sortOrder || 1
    const totalSamples = taskInfo.totalSamples || bottleGroup.totalCount || 1
    const sampleNumber = `${taskCode}（${sampleIndex}/${totalSamples}）`
    
    // 构建采样点位：点位名称 （周期{周期数}）
    const pointName = taskInfo.pointName || taskInfo.location || '采样点位'
    const cycleNumber = taskInfo.cycleNumber || bottleGroup.cycleNumber || 1
    const samplingPoint = `${pointName} （周期${cycleNumber}）`
    
    // 格式化日期
    const formatDate = (dateStr) => {
      if (!dateStr) return new Date().toISOString().split('T')[0]
      const date = new Date(dateStr)
      return date.toISOString().split('T')[0]
    }
    
    const labelData = {
      sampleCategory: taskInfo.detectionCategory || taskInfo.sampleType || '环境样品',
      sampleNumber: sampleNumber,
      samplingDate: formatDate(bottleGroup.samplingDate || taskInfo.samplingDate),
      samplingPoint: samplingPoint,
      testItems: bottleGroup.testItems || taskInfo.testItems || '常规检测',
      container: bottleGroup.containerInfo || bottleGroup.bottleType || '样品瓶',
      storageMethod: bottleGroup.storageMethod || '常温保存',
      companyName: '浙江求实环境监测有限公司',
      pageNumber: '1/1'
    }
    
    this.setData({ labelData }, () => {
      console.log('标签数据设置完成，准备绘制预览')
      // 数据设置完成后绘制预览
      setTimeout(() => {
        this.drawPreview()
      }, 50)
    })
    console.log('生成的标签数据:', labelData)
  },

  // 加载打印机设置
  loadPrinterSettings() {
    try {
      const connectedDevice = wx.getStorageSync('connectedDevice')
      const printerSettings = wx.getStorageSync('printerSettings') || this.data.printerSettings
      
      this.setData({
        connectedDevice,
        printerSettings
      })
    } catch (error) {
      console.error('加载打印机设置失败:', error)
    }
  },

  // 绘制预览
  drawPreview() {
    const { labelData } = this.data
    console.log('开始绘制预览，标签数据:', labelData)

    if (!labelData) {
      console.log('标签数据为空，无法绘制')
      return
    }

    const ctx = wx.createCanvasContext('previewCanvas', this)
    const { canvasWidth, canvasHeight } = this.data

    console.log('Canvas尺寸:', canvasWidth, canvasHeight)
    
    // 清空画布
    ctx.clearRect(0, 0, canvasWidth, canvasHeight)
    
    // 设置背景
    ctx.setFillStyle('#ffffff')
    ctx.fillRect(0, 0, canvasWidth, canvasHeight)
    
    // 绘制边框
    ctx.setStrokeStyle('#000000')
    ctx.setLineWidth(2)
    ctx.strokeRect(5, 5, canvasWidth - 10, canvasHeight - 10)
    
    // 设置字体
    ctx.setFillStyle('#000000')
    ctx.setFontSize(12)
    
    // 绘制标签内容
    let yPos = 25
    const leftMargin = 15
    const lineHeight = 18
    
    // 样品类别
    ctx.fillText(`样品类别：${labelData.sampleCategory}`, leftMargin, yPos)
    yPos += lineHeight
    
    // 样品编号
    ctx.setFontSize(10)
    ctx.fillText(`样品编号：${labelData.sampleNumber}`, leftMargin, yPos)
    yPos += lineHeight
    
    // 采样日期
    ctx.fillText(`采样日期：${labelData.samplingDate}`, leftMargin, yPos)
    yPos += lineHeight
    
    // 采样点位
    ctx.fillText(`采样点位：${labelData.samplingPoint}`, leftMargin, yPos)
    yPos += lineHeight
    
    // 检测项目
    ctx.fillText(`检测项目：${labelData.testItems}`, leftMargin, yPos)
    yPos += lineHeight
    
    // 保存容器
    ctx.fillText(`保存容器：${labelData.container}`, leftMargin, yPos)
    yPos += lineHeight
    
    // 保存方式
    ctx.fillText(`保存方式：${labelData.storageMethod}`, leftMargin, yPos)
    yPos += lineHeight
    
    // 绘制二维码区域
    const qrSize = 60
    const qrX = canvasWidth - qrSize - 15
    const qrY = 25
    
    ctx.setStrokeStyle('#cccccc')
    ctx.setLineWidth(1)
    ctx.strokeRect(qrX, qrY, qrSize, qrSize)
    
    // 二维码占位文字
    ctx.setFontSize(10)
    ctx.setTextAlign('center')
    ctx.fillText('二维码', qrX + qrSize/2, qrY + qrSize/2)
    
    // 底部公司信息
    ctx.setTextAlign('left')
    ctx.setFontSize(8)
    ctx.fillText(labelData.companyName, leftMargin, canvasHeight - 15)
    
    ctx.setTextAlign('right')
    ctx.fillText(labelData.pageNumber, canvasWidth - 15, canvasHeight - 15)

    // 执行绘制
    ctx.draw(false, () => {
      console.log('Canvas绘制完成')
    })
  },

  // 重新绘制预览
  redrawPreview() {
    console.log('重新绘制预览')
    // 重新生成标签数据
    this.generateLabelData()
    // 延迟绘制确保数据更新
    setTimeout(() => {
      this.drawPreview()
    }, 100)
  },

  // 打印标签
  async printLabel() {
    const { connectedDevice, labelData, printerSettings } = this.data
    
    if (!connectedDevice) {
      wx.showModal({
        title: '提示',
        content: '请先配置打印机',
        confirmText: '去配置',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/printer/config'
            })
          }
        }
      })
      return
    }
    
    if (!labelData) {
      wx.showToast({
        title: '标签数据不完整',
        icon: 'none'
      })
      return
    }
    
    try {
      wx.showLoading({ title: '打印中...' })
      
      // 生成打印内容
      const printContent = this.generatePrintContent()
      
      // 生成CPCL指令
      const commands = this.generateCPCLCommands(printContent, printerSettings)
      
      // 发送打印数据
      await this.sendBLEData(connectedDevice, commands)
      
      wx.hideLoading()
      wx.showToast({
        title: '打印完成',
        icon: 'success'
      })
      
      // 返回上一页
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
      
    } catch (error) {
      wx.hideLoading()
      console.error('打印失败:', error)
      wx.showModal({
        title: '打印失败',
        content: error.message || '打印过程中出现错误',
        showCancel: false
      })
    }
  },

  // 生成打印内容
  generatePrintContent() {
    const { labelData } = this.data
    
    return {
      content: [
        { type: 'text', x: 5, y: 10, text: `样品类别：${labelData.sampleCategory}`, fontSize: 12 },
        { type: 'text', x: 5, y: 32, text: `样品编号：${labelData.sampleNumber}`, fontSize: 10 },
        { type: 'text', x: 5, y: 54, text: `采样日期：${labelData.samplingDate}`, fontSize: 10 },
        { type: 'text', x: 5, y: 76, text: `采样点位：${labelData.samplingPoint}`, fontSize: 10 },
        { type: 'text', x: 5, y: 98, text: `检测项目：${labelData.testItems}`, fontSize: 10 },
        { type: 'text', x: 5, y: 120, text: `保存容器：${labelData.container}`, fontSize: 10 },
        { type: 'text', x: 5, y: 142, text: `保存方式：${labelData.storageMethod}`, fontSize: 10 },
        { type: 'qrcode', x: 200, y: 20, text: labelData.sampleNumber, size: 60 },
        { type: 'text', x: 5, y: 180, text: labelData.companyName, fontSize: 8 },
        { type: 'text', x: 200, y: 180, text: labelData.pageNumber, fontSize: 8 }
      ]
    }
  },

  // 生成CPCL打印指令
  generateCPCLCommands(content, settings) {
    const commands = []
    
    // CPCL标签开始
    const labelHeight = Math.min(settings.paperHeight * 6, 400)
    const labelStart = `! 0 200 200 ${labelHeight} 1\r\n`
    commands.push(...this.stringToBytes(labelStart))
    
    // 添加GB2312编码声明
    commands.push(...this.stringToBytes('ENCODING GB2312\r\n'))
    
    // 遍历打印内容
    content.content.forEach((item) => {
      if (item.type === 'text') {
        // 使用字体3和GBK编码
        const textCmd = `TEXT 3 0 ${item.x * 4} ${item.y * 4} `
        commands.push(...this.stringToBytes(textCmd))
        
        const gbkEncoder = new TextEncoder('gbk')
        const textBytes = Array.from(gbkEncoder.encode(item.text))
        commands.push(...textBytes)
        commands.push(...this.stringToBytes('\r\n'))
        
      } else if (item.type === 'qrcode') {
        // 二维码
        const qrCmd = `BARCODE QR ${item.x * 4} ${item.y * 4} M 2 U 4\r\n`
        commands.push(...this.stringToBytes(qrCmd))
        commands.push(...this.stringToBytes(`MA,${item.text}\r\n`))
        commands.push(...this.stringToBytes('ENDQR\r\n'))
      }
    })
    
    // 打印标签
    commands.push(...this.stringToBytes('PRINT\r\n'))
    
    return new Uint8Array(commands)
  },

  // 字符串转字节数组
  stringToBytes(str) {
    const bytes = []
    for (let i = 0; i < str.length; i++) {
      bytes.push(str.charCodeAt(i))
    }
    return bytes
  },

  // 发送BLE数据
  async sendBLEData(device, data) {
    const maxChunkSize = 20
    const chunks = []
    
    for (let i = 0; i < data.length; i += maxChunkSize) {
      chunks.push(data.slice(i, i + maxChunkSize))
    }
    
    for (let i = 0; i < chunks.length; i++) {
      await new Promise((resolve, reject) => {
        wx.writeBLECharacteristicValue({
          deviceId: device.deviceId,
          serviceId: device.serviceId,
          characteristicId: device.characteristicId,
          value: chunks[i].buffer,
          success: resolve,
          fail: reject
        })
      })
      
      // 添加延迟避免数据丢失
      if (i < chunks.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 50))
      }
    }
  },

  // 返回上一页
  goBack() {
    wx.navigateBack()
  }
})
