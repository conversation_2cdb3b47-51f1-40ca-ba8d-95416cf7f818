/* pages/debug/test.wxss */
.page-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #303133;
  margin-bottom: 20rpx;
}

.test-section {
  margin-bottom: 40rpx;
  padding-bottom: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.test-section:last-child {
  border-bottom: none;
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 16rpx;
}

.status-text {
  display: block;
  font-size: 24rpx;
  margin-bottom: 16rpx;
  padding: 8rpx 16rpx;
  border-radius: 8rpx;
}

.status-unknown {
  background: #f5f5f5;
  color: #909399;
}

.status-testing {
  background: #fdf6ec;
  color: #E6A23C;
}

.status-success {
  background: #f0f9f0;
  color: #67C23A;
}

.status-error {
  background: #fef0f0;
  color: #F56C6C;
}

.test-input {
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  margin-bottom: 16rpx;
  background: #f8f9fa;
  border: 1rpx solid #e9ecef;
  border-radius: 8rpx;
  font-size: 26rpx;
  box-sizing: border-box;
}

/* 验证码 */
.captcha-section {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 16rpx;
}

.captcha-input {
  flex: 1;
  height: 80rpx;
  padding: 0 20rpx;
  background: #f8f9fa;
  border: 1rpx solid #e9ecef;
  border-radius: 8rpx;
  font-size: 26rpx;
  box-sizing: border-box;
}

.captcha-image {
  width: 160rpx;
  height: 80rpx;
  background: #f8f9fa;
  border: 1rpx solid #e9ecef;
  border-radius: 8rpx;
  cursor: pointer;
}

.test-btn {
  width: 100%;
  height: 80rpx;
  background: #409EFF;
  color: white;
  border: none;
  border-radius: 8rpx;
  font-size: 26rpx;
  font-weight: bold;
}

.result-area {
  height: 300rpx;
  background: #f8f9fa;
  border: 1rpx solid #e9ecef;
  border-radius: 8rpx;
  padding: 20rpx;
}

.result-text {
  font-size: 22rpx;
  color: #303133;
  line-height: 1.6;
  white-space: pre-wrap;
  word-break: break-all;
}
