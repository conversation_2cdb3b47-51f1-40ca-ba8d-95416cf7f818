// pages/debug/test.js
const app = getApp()

Page({
  data: {
    connectionStatus: 'unknown',
    connectionText: '未测试',
    testUsername: 'admin',
    testPassword: 'admin123',
    testResult: '点击按钮开始测试...',
    captchaCode: '',
    captchaUrl: '',
    captchaUuid: '',
    captchaEnabled: false
  },

  onLoad() {
    console.log('API测试页面加载')
    this.testConnection()
    this.getCaptcha()
  },

  // 测试后端连接
  async testConnection() {
    this.setData({
      connectionStatus: 'testing',
      connectionText: '测试中...',
      testResult: '正在测试后端连接...'
    })

    try {
      const response = await new Promise((resolve, reject) => {
        wx.request({
          url: app.globalData.baseURL + '/docs',
          method: 'GET',
          timeout: 5000,
          success: resolve,
          fail: reject
        })
      })

      if (response.statusCode === 200) {
        this.setData({
          connectionStatus: 'success',
          connectionText: '连接成功',
          testResult: `后端连接成功！\n状态码: ${response.statusCode}\n服务器: ${app.globalData.baseURL}`
        })
      } else {
        this.setData({
          connectionStatus: 'error',
          connectionText: '连接异常',
          testResult: `连接异常\n状态码: ${response.statusCode}\n响应: ${JSON.stringify(response.data, null, 2)}`
        })
      }
    } catch (error) {
      this.setData({
        connectionStatus: 'error',
        connectionText: '连接失败',
        testResult: `连接失败\n错误信息: ${error.message || error.errMsg}\n服务器: ${app.globalData.baseURL}`
      })
    }
  },

  // 测试登录API
  async testLogin() {
    const { testUsername, testPassword, captchaEnabled, captchaCode, captchaUuid } = this.data

    if (!testUsername || !testPassword) {
      this.setData({
        testResult: '请输入用户名和密码'
      })
      return
    }

    if (captchaEnabled && !captchaCode) {
      this.setData({
        testResult: '请输入验证码'
      })
      return
    }

    this.setData({
      testResult: '正在测试登录API...'
    })

    try {
      // 构建请求数据
      let formData = `username=${encodeURIComponent(testUsername)}&password=${encodeURIComponent(testPassword)}`

      // 如果启用了验证码，添加验证码参数
      if (captchaEnabled) {
        formData += `&code=${encodeURIComponent(captchaCode)}&uuid=${encodeURIComponent(captchaUuid)}`
      }

      const result = await app.request({
        url: '/login',
        method: 'POST',
        data: formData,
        header: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      })

      this.setData({
        testResult: `登录API测试结果:\n${JSON.stringify(result, null, 2)}`
      })

    } catch (error) {
      this.setData({
        testResult: `登录API测试失败:\n错误: ${error.message}\n详情: ${JSON.stringify(error, null, 2)}`
      })
      // 登录失败时刷新验证码
      if (this.data.captchaEnabled) {
        this.getCaptcha()
      }
    }
  },

  // 输入事件
  onUsernameInput(e) {
    this.setData({
      testUsername: e.detail.value
    })
  },

  onPasswordInput(e) {
    this.setData({
      testPassword: e.detail.value
    })
  },

  // 获取验证码
  async getCaptcha() {
    try {
      const result = await app.request({
        url: '/captchaImage',
        method: 'GET'
      })

      if (result.code === 200) {
        // 数据直接在result根级别，不在result.data中
        const { captchaEnabled, img, uuid } = result

        this.setData({
          captchaEnabled: captchaEnabled,
          captchaUrl: img ? `data:image/png;base64,${img}` : '',
          captchaUuid: uuid || '',
          captchaCode: ''
        })

        console.log('验证码获取成功:', { captchaEnabled, uuid })
      } else {
        console.error('获取验证码失败:', result.message)
        this.setData({
          captchaEnabled: false
        })
      }
    } catch (error) {
      console.error('获取验证码异常:', error)
      this.setData({
        captchaEnabled: false
      })
    }
  },

  // 验证码输入
  onCaptchaInput(e) {
    this.setData({
      captchaCode: e.detail.value
    })
  },

  // 刷新验证码
  refreshCaptcha() {
    this.getCaptcha()
  }
})
