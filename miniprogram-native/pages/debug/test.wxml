<!--pages/debug/test.wxml-->
<view class="container">
  <text class="page-title">API连接测试</text>

  <view class="card">
    <view class="test-section">
      <text class="section-title">后端连接状态</text>
      <text class="status-text status-{{connectionStatus}}">{{connectionText}}</text>
      <button class="test-btn" bindtap="testConnection">测试连接</button>
    </view>

    <view class="test-section">
      <text class="section-title">登录API测试</text>
      <input class="test-input" placeholder="用户名" value="{{testUsername}}" bindinput="onUsernameInput" />
      <input class="test-input" placeholder="密码" type="password" value="{{testPassword}}" bindinput="onPasswordInput" />

      <!-- 验证码 -->
      <view wx:if="{{captchaEnabled}}" class="captcha-section">
        <input
          class="captcha-input"
          placeholder="请输入验证码"
          value="{{captchaCode}}"
          bindinput="onCaptchaInput"
        />
        <image
          class="captcha-image"
          src="{{captchaUrl}}"
          bindtap="refreshCaptcha"
          mode="aspectFit"
        />
      </view>

      <button class="test-btn" bindtap="testLogin">测试登录</button>
    </view>

    <view class="test-section">
      <text class="section-title">测试结果</text>
      <scroll-view class="result-area" scroll-y>
        <text class="result-text">{{testResult}}</text>
      </scroll-view>
    </view>
  </view>
</view>
