/* pages/profile/profile.wxss */

/* 用户信息 */
.user-info {
  margin-bottom: 30rpx;
}

.user-header {
  display: flex;
  align-items: center;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  margin-right: 30rpx;
  border: 4rpx solid #f0f0f0;
}

.user-details {
  flex: 1;
}

.username {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #303133;
  margin-bottom: 12rpx;
}

.role {
  font-size: 26rpx;
  color: #909399;
}

/* 菜单列表 */
.menu-list {
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  margin-bottom: 30rpx;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: background-color 0.2s;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:active {
  background-color: #f8f9fa;
}

.menu-icon {
  width: 60rpx;
  height: 60rpx;
  margin-right: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f0f9ff;
  border-radius: 50%;
}

.menu-icon image {
  width: 36rpx;
  height: 36rpx;
}

.menu-text {
  flex: 1;
  font-size: 28rpx;
  color: #303133;
}

.menu-arrow {
  width: 24rpx;
  height: 24rpx;
  opacity: 0.6;
}

/* 退出登录 */
.logout-section {
  padding: 0 20rpx;
}

.logout-btn {
  width: 100%;
  height: 80rpx;
  background: #F56C6C;
  color: white;
  border: none;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: bold;
}
