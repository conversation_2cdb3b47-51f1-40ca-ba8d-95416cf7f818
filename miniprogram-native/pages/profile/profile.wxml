<!--pages/profile/profile.wxml-->
<view class="container">
  <!-- 用户信息 -->
  <view class="user-info card">
    <view class="user-header">
      <image class="avatar" src="{{userInfo.avatar || '/static/images/default-avatar.png'}}" mode="aspectFill"></image>
      <view class="user-details">
        <text class="username">{{userInfo.username || '未登录'}}</text>
        <text class="role">{{userInfo.role || '请先登录'}}</text>
      </view>
    </view>
  </view>

  <!-- 功能菜单 -->
  <view class="menu-list">
    <view class="menu-item" bindtap="goToSettings">
      <view class="menu-icon">
        <image src="/static/icons/settings.png" mode="aspectFit"></image>
      </view>
      <text class="menu-text">系统设置</text>
      <image class="menu-arrow" src="/static/icons/arrow-right.png" mode="aspectFit"></image>
    </view>
    
    <view class="menu-item" bindtap="goToHelp">
      <view class="menu-icon">
        <image src="/static/icons/help.png" mode="aspectFit"></image>
      </view>
      <text class="menu-text">帮助中心</text>
      <image class="menu-arrow" src="/static/icons/arrow-right.png" mode="aspectFit"></image>
    </view>
    
    <view class="menu-item" bindtap="goToAbout">
      <view class="menu-icon">
        <image src="/static/icons/about.png" mode="aspectFit"></image>
      </view>
      <text class="menu-text">关于我们</text>
      <image class="menu-arrow" src="/static/icons/arrow-right.png" mode="aspectFit"></image>
    </view>
  </view>

  <!-- 退出登录 -->
  <view class="logout-section">
    <button class="logout-btn" bindtap="logout">退出登录</button>
  </view>
</view>
