// pages/profile/profile.js
const app = getApp()

Page({
  data: {
    userInfo: {}
  },

  onLoad() {
    console.log('个人中心页面加载')
  },

  onShow() {
    // 检查登录状态
    if (!app.globalData.isLoggedIn) {
      wx.reLaunch({
        url: '/pages/login/login'
      })
      return
    }
    
    this.setData({
      userInfo: app.globalData.userInfo || {}
    })
  },

  // 系统设置
  goToSettings() {
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    })
  },

  // 帮助中心
  goToHelp() {
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    })
  },

  // 关于我们
  goToAbout() {
    wx.showModal({
      title: '关于我们',
      content: 'LIMS实验室信息管理系统\n版本: v1.0.0\n\n专业的实验室管理解决方案',
      showCancel: false,
      confirmText: '知道了'
    })
  },

  // 退出登录
  logout() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          app.logout()
        }
      }
    })
  }
})
