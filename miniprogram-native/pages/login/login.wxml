<!--pages/login/login.wxml-->
<view class="login-container">
  <!-- 背景装饰 -->
  <view class="bg-decoration">
    <view class="circle circle-1"></view>
    <view class="circle circle-2"></view>
    <view class="circle circle-3"></view>
  </view>

  <!-- Logo和标题 -->
  <view class="header">
    <image class="logo" src="/static/images/logo.png" mode="aspectFit"></image>
    <text class="app-name">LIMS实验室管理</text>
    <text class="app-desc">专业的实验室信息管理系统</text>
  </view>

  <!-- 登录表单 -->
  <view class="login-form">
    <view class="form-item">
      <view class="input-wrapper">
        <image class="input-icon" src="/static/icons/user.png" mode="aspectFit"></image>
        <input 
          class="input-field" 
          type="text" 
          placeholder="请输入用户名" 
          value="{{username}}" 
          bindinput="onUsernameInput"
          maxlength="50"
        />
      </view>
    </view>

    <view class="form-item">
      <view class="input-wrapper">
        <image class="input-icon" src="/static/icons/lock.png" mode="aspectFit"></image>
        <input 
          class="input-field" 
          type="{{showPassword ? 'text' : 'password'}}" 
          placeholder="请输入密码" 
          value="{{password}}" 
          bindinput="onPasswordInput"
          maxlength="50"
        />
        <image 
          class="toggle-password" 
          src="/static/icons/{{showPassword ? 'eye-off' : 'eye'}}.png" 
          mode="aspectFit"
          bindtap="togglePassword"
        ></image>
      </view>
    </view>

    <!-- 验证码 -->
    <view wx:if="{{captchaEnabled}}" class="captcha-section">
      <input
        class="captcha-input"
        placeholder="请输入验证码"
        value="{{captchaCode}}"
        bindinput="onCaptchaInput"
      />
      <image
        class="captcha-image"
        src="{{captchaUrl}}"
        bindtap="refreshCaptcha"
        mode="aspectFit"
      />
    </view>

    <!-- 记住密码 -->
    <view class="form-options">
      <view class="remember-password" bindtap="toggleRemember">
        <image
          class="checkbox"
          src="/static/icons/{{rememberPassword ? 'checkbox-checked' : 'checkbox'}}.png"
          mode="aspectFit"
        ></image>
        <text class="checkbox-text">记住密码</text>
      </view>
      <text class="forgot-password" bindtap="forgotPassword">忘记密码？</text>
    </view>

    <!-- 登录按钮 -->
    <button 
      class="login-btn {{loading ? 'btn-disabled' : ''}}" 
      bindtap="login"
      disabled="{{loading}}"
    >
      <view wx:if="{{loading}}" class="btn-loading">
        <view class="loading-spinner"></view>
        <text>登录中...</text>
      </view>
      <text wx:else>登录</text>
    </button>

    <!-- 快速登录 -->
    <view class="quick-login">
      <view class="divider">
        <view class="divider-line"></view>
        <text class="divider-text">快速登录</text>
        <view class="divider-line"></view>
      </view>
      
      <view class="quick-login-methods">
        <button class="quick-login-btn demo-login" bindtap="demoLogin">
          <image src="/static/icons/demo.png" mode="aspectFit"></image>
          <text>演示账号</text>
        </button>
        <button class="quick-login-btn wechat-login" bindtap="wechatLogin">
          <image src="/static/icons/wechat.png" mode="aspectFit"></image>
          <text>微信登录</text>
        </button>
        <button class="quick-login-btn debug-btn" bindtap="debugCaptcha">
          <image src="/static/icons/debug.png" mode="aspectFit"></image>
          <text>调试验证码</text>
        </button>
      </view>
    </view>
  </view>

  <!-- 底部信息 -->
  <view class="footer">
    <text class="version">版本 v1.0.0</text>
    <view class="links">
      <text class="link" bindtap="showPrivacy">隐私政策</text>
      <text class="separator">|</text>
      <text class="link" bindtap="showTerms">服务条款</text>
    </view>
  </view>
</view>

<!-- 错误提示弹窗 -->
<view wx:if="{{showError}}" class="error-modal" bindtap="hideError">
  <view class="error-content" catchtap="stopPropagation">
    <view class="error-header">
      <image class="error-icon" src="/static/icons/error.png" mode="aspectFit"></image>
      <text class="error-title">登录失败</text>
    </view>
    <text class="error-message">{{errorMessage}}</text>
    <button class="error-btn" bindtap="hideError">确定</button>
  </view>
</view>
