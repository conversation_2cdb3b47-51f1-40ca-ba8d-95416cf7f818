// pages/login/login.js
const app = getApp()

Page({
  data: {
    username: '',
    password: '',
    showPassword: false,
    rememberPassword: false,
    loading: false,
    showError: false,
    errorMessage: '',
    // 验证码相关
    captchaEnabled: false,
    captchaCode: '',
    captchaUrl: '',
    captchaUuid: ''
  },

  onLoad() {
    console.log('登录页面加载')
    this.loadSavedCredentials()
    this.getCaptcha()
  },

  onShow() {
    // 如果已经登录，直接跳转到首页
    if (app.globalData.isLoggedIn) {
      wx.reLaunch({
        url: '/pages/index/index'
      })
    }
  },

  // 加载保存的登录信息
  loadSavedCredentials() {
    const savedUsername = wx.getStorageSync('savedUsername')
    const savedPassword = wx.getStorageSync('savedPassword')
    const rememberPassword = wx.getStorageSync('rememberPassword')

    if (rememberPassword && savedUsername) {
      this.setData({
        username: savedUsername,
        password: savedPassword || '',
        rememberPassword: true
      })
    }
  },

  // 用户名输入
  onUsernameInput(e) {
    this.setData({
      username: e.detail.value
    })
  },

  // 密码输入
  onPasswordInput(e) {
    this.setData({
      password: e.detail.value
    })
  },

  // 切换密码显示
  togglePassword() {
    this.setData({
      showPassword: !this.data.showPassword
    })
  },

  // 切换记住密码
  toggleRemember() {
    this.setData({
      rememberPassword: !this.data.rememberPassword
    })
  },

  // 登录
  async login() {
    const { username, password, rememberPassword, captchaEnabled, captchaCode, captchaUuid } = this.data

    // 验证输入
    if (!username.trim()) {
      this.showErrorMessage('请输入用户名')
      return
    }

    if (!password.trim()) {
      this.showErrorMessage('请输入密码')
      return
    }

    // 如果启用了验证码，检查验证码输入
    if (captchaEnabled && !captchaCode.trim()) {
      this.showErrorMessage('请输入验证码')
      return
    }

    this.setData({ loading: true })

    try {
      // 构建请求数据
      let formData = `username=${encodeURIComponent(username.trim())}&password=${encodeURIComponent(password.trim())}`

      // 如果启用了验证码，添加验证码参数
      if (captchaEnabled) {
        formData += `&code=${encodeURIComponent(captchaCode.trim())}&uuid=${encodeURIComponent(captchaUuid)}`
      }

      // 调用登录接口
      const result = await app.request({
        url: '/login',
        method: 'POST',
        data: formData,
        header: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      })

      // 登录成功
      if (result.code === 200) {
        // 登录接口只返回token，没有user信息
        const { token } = result

        // 先保存token到全局数据，用于后续请求
        app.globalData.token = token
        wx.setStorageSync('token', token)

        // 获取用户信息
        try {
          const userInfoResult = await app.request({
            url: '/getInfo',
            method: 'GET'
          })

          if (userInfoResult.code === 200) {
            // 获取用户信息成功，完成登录
            console.log('用户信息获取成功:', userInfoResult)

            // 解析用户信息，映射字段名
            const rawUserInfo = userInfoResult.user || {}
            const userInfo = {
              userId: rawUserInfo.userId,
              username: rawUserInfo.userName || rawUserInfo.nickName || '用户',
              nickName: rawUserInfo.nickName,
              userName: rawUserInfo.userName,
              avatar: rawUserInfo.avatar,
              role: Array.isArray(rawUserInfo.role) && rawUserInfo.role.length > 0
                    ? rawUserInfo.role[0].roleName || '普通用户'
                    : '普通用户',
              roles: userInfoResult.roles || [],
              permissions: userInfoResult.permissions || []
            }

            console.log('映射后的用户信息:', userInfo)
            app.login(userInfo, token)

            // 保存记住密码设置
            if (rememberPassword) {
              wx.setStorageSync('savedUsername', username)
              wx.setStorageSync('savedPassword', password)
              wx.setStorageSync('rememberPassword', true)
            } else {
              wx.removeStorageSync('savedUsername')
              wx.removeStorageSync('savedPassword')
              wx.removeStorageSync('rememberPassword')
            }

            // 跳转到首页
            wx.reLaunch({
              url: '/pages/index/index'
            })

            app.showToast('登录成功', 'success')
          } else {
            throw new Error(userInfoResult.message || '获取用户信息失败')
          }
        } catch (userInfoError) {
          console.error('获取用户信息失败:', userInfoError)
          this.showErrorMessage('登录成功但获取用户信息失败，请重试')
        }

      } else {
        this.showErrorMessage(result.message || '登录失败')
        // 登录失败时刷新验证码
        if (this.data.captchaEnabled) {
          this.getCaptcha()
        }
      }

    } catch (error) {
      console.error('登录失败:', error)
      this.showErrorMessage(error.message || '网络连接失败，请检查网络设置')
      // 登录失败时刷新验证码
      if (this.data.captchaEnabled) {
        this.getCaptcha()
      }
    } finally {
      this.setData({ loading: false })
    }
  },

  // 演示账号登录
  async demoLogin() {
    this.setData({
      username: 'admin',
      password: 'admin123'
    })

    // 自动登录
    await this.login()
  },

  // 微信登录
  async wechatLogin() {
    try {
      // 获取微信登录code
      const loginRes = await new Promise((resolve, reject) => {
        wx.login({
          success: resolve,
          fail: reject
        })
      })

      if (!loginRes.code) {
        throw new Error('获取微信登录code失败')
      }

      this.setData({ loading: true })

      // 调用微信登录接口
      const result = await app.request({
        url: '/wechat-login',
        method: 'POST',
        data: {
          code: loginRes.code
        }
      })

      if (result.code === 200) {
        const { user, token } = result.data
        app.login(user, token)

        wx.reLaunch({
          url: '/pages/index/index'
        })

        app.showToast('登录成功', 'success')
      } else {
        this.showErrorMessage(result.message || '微信登录失败')
      }

    } catch (error) {
      console.error('微信登录失败:', error)
      this.showErrorMessage('微信登录失败，请重试')
    } finally {
      this.setData({ loading: false })
    }
  },

  // 忘记密码
  forgotPassword() {
    wx.showModal({
      title: '忘记密码',
      content: '请联系系统管理员重置密码\n联系电话: 400-xxx-xxxx',
      showCancel: false,
      confirmText: '知道了'
    })
  },

  // 显示隐私政策
  showPrivacy() {
    wx.showModal({
      title: '隐私政策',
      content: '我们重视您的隐私保护，详细的隐私政策请访问官网查看。',
      showCancel: false,
      confirmText: '知道了'
    })
  },

  // 显示服务条款
  showTerms() {
    wx.showModal({
      title: '服务条款',
      content: '使用本应用即表示您同意我们的服务条款，详细条款请访问官网查看。',
      showCancel: false,
      confirmText: '知道了'
    })
  },

  // 显示错误信息
  showErrorMessage(message) {
    this.setData({
      showError: true,
      errorMessage: message
    })
  },

  // 隐藏错误弹窗
  hideError() {
    this.setData({
      showError: false,
      errorMessage: ''
    })
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止事件冒泡
  },

  // 获取验证码
  async getCaptcha() {
    try {
      console.log('开始获取验证码...')
      const result = await app.request({
        url: '/captchaImage',
        method: 'GET'
      })

      console.log('验证码接口响应:', result)

      if (result.code === 200) {
        // 数据直接在result根级别，不在result.data中
        const { captchaEnabled, img, uuid } = result

        console.log('验证码配置:', { captchaEnabled, hasImg: !!img, uuid })

        this.setData({
          captchaEnabled: captchaEnabled,
          captchaUrl: img ? `data:image/png;base64,${img}` : '',
          captchaUuid: uuid || '',
          captchaCode: '' // 清空之前输入的验证码
        })

        if (captchaEnabled) {
          console.log('验证码已启用，应该显示验证码输入框')
        } else {
          console.log('验证码未启用，不显示验证码输入框')
        }
      } else {
        console.error('获取验证码失败:', result.message)
        // 如果获取验证码失败，禁用验证码功能
        this.setData({
          captchaEnabled: false
        })
      }
    } catch (error) {
      console.error('获取验证码异常:', error)
      // 如果获取验证码异常，禁用验证码功能
      this.setData({
        captchaEnabled: false
      })
    }
  },

  // 刷新验证码
  refreshCaptcha() {
    this.getCaptcha()
  },

  // 验证码输入
  onCaptchaInput(e) {
    this.setData({
      captchaCode: e.detail.value
    })
  },

  // 调试验证码
  debugCaptcha() {
    const { captchaEnabled, captchaUrl, captchaUuid } = this.data

    const debugInfo = `
验证码状态调试信息：
- 验证码启用: ${captchaEnabled}
- 验证码URL: ${captchaUrl ? '已获取' : '未获取'}
- 验证码UUID: ${captchaUuid || '无'}
- URL长度: ${captchaUrl ? captchaUrl.length : 0}

请查看后端日志获取详细信息
    `.trim()

    console.log('验证码调试信息:', debugInfo)

    wx.showModal({
      title: '验证码调试信息',
      content: debugInfo,
      showCancel: true,
      cancelText: '重新获取',
      confirmText: '查看日志',
      success: (res) => {
        if (res.cancel) {
          // 重新获取验证码
          console.log('重新获取验证码...')
          this.getCaptcha()
        } else {
          // 提示查看后端日志
          wx.showToast({
            title: '请查看后端控制台日志',
            icon: 'none',
            duration: 3000
          })
        }
      }
    })
  }
})
