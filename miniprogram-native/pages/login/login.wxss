/* pages/login/login.wxss */

.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  padding: 60rpx 40rpx 40rpx;
  box-sizing: border-box;
}

/* 背景装饰 */
.bg-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 200rpx;
  height: 200rpx;
  top: 10%;
  right: -50rpx;
  animation-delay: 0s;
}

.circle-2 {
  width: 150rpx;
  height: 150rpx;
  top: 60%;
  left: -30rpx;
  animation-delay: 2s;
}

.circle-3 {
  width: 100rpx;
  height: 100rpx;
  top: 30%;
  left: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

/* 头部 */
.header {
  text-align: center;
  margin-bottom: 80rpx;
  z-index: 1;
}

.logo {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
}

.app-name {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 16rpx;
}

.app-desc {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 登录表单 */
.login-form {
  flex: 1;
  z-index: 1;
}

.form-item {
  margin-bottom: 40rpx;
}

.input-wrapper {
  position: relative;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50rpx;
  display: flex;
  align-items: center;
  padding: 0 30rpx;
  height: 100rpx;
  backdrop-filter: blur(10rpx);
}

.input-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
  opacity: 0.6;
}

.input-field {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  background: transparent;
  border: none;
  outline: none;
}

.input-field::placeholder {
  color: #999;
}

.toggle-password {
  width: 40rpx;
  height: 40rpx;
  opacity: 0.6;
  margin-left: 20rpx;
}

/* 验证码 */
.captcha-section {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.captcha-input {
  flex: 1;
  height: 80rpx;
  padding: 0 30rpx;
  background: rgba(255, 255, 255, 0.1);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 40rpx;
  color: white;
  font-size: 28rpx;
}

.captcha-input::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.captcha-image {
  width: 160rpx;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  cursor: pointer;
}

/* 表单选项 */
.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 60rpx;
}

.remember-password {
  display: flex;
  align-items: center;
}

.checkbox {
  width: 32rpx;
  height: 32rpx;
  margin-right: 12rpx;
}

.checkbox-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
}

.forgot-password {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
  text-decoration: underline;
}

/* 登录按钮 */
.login-btn {
  width: 100%;
  height: 100rpx;
  background: linear-gradient(135deg, #409EFF, #67C23A);
  color: white;
  border: none;
  border-radius: 50rpx;
  font-size: 32rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 60rpx;
  box-shadow: 0 8rpx 20rpx rgba(64, 158, 255, 0.3);
  transition: all 0.3s;
}

.login-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 10rpx rgba(64, 158, 255, 0.3);
}

.login-btn.btn-disabled {
  opacity: 0.6;
  transform: none;
}

.btn-loading {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.loading-spinner {
  width: 32rpx;
  height: 32rpx;
  border: 3rpx solid rgba(255, 255, 255, 0.3);
  border-top: 3rpx solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 快速登录 */
.quick-login {
  margin-bottom: 40rpx;
}

.divider {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
}

.divider-line {
  flex: 1;
  height: 2rpx;
  background: rgba(255, 255, 255, 0.3);
}

.divider-text {
  padding: 0 30rpx;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}

.quick-login-methods {
  display: flex;
  gap: 30rpx;
}

.quick-login-btn {
  flex: 1;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.1);
  border: 2rpx solid rgba(255, 255, 255, 0.2);
  border-radius: 40rpx;
  color: white;
  font-size: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  backdrop-filter: blur(10rpx);
}

.quick-login-btn image {
  width: 32rpx;
  height: 32rpx;
}

.demo-login {
  background: rgba(230, 162, 60, 0.2);
  border-color: rgba(230, 162, 60, 0.3);
}

.wechat-login {
  background: rgba(103, 194, 58, 0.2);
  border-color: rgba(103, 194, 58, 0.3);
}

.debug-btn {
  background: rgba(255, 107, 107, 0.2);
  border-color: rgba(255, 107, 107, 0.3);
}

/* 底部信息 */
.footer {
  text-align: center;
  z-index: 1;
}

.version {
  display: block;
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 20rpx;
}

.links {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20rpx;
}

.link {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.8);
  text-decoration: underline;
}

.separator {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.6);
}

/* 错误弹窗 */
.error-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 40rpx;
  box-sizing: border-box;
}

.error-content {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  max-width: 600rpx;
  width: 100%;
  text-align: center;
}

.error-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30rpx;
}

.error-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 20rpx;
}

.error-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #F56C6C;
}

.error-message {
  font-size: 28rpx;
  color: #606266;
  line-height: 1.6;
  margin-bottom: 40rpx;
}

.error-btn {
  width: 200rpx;
  height: 70rpx;
  background: #F56C6C;
  color: white;
  border: none;
  border-radius: 35rpx;
  font-size: 28rpx;
}
