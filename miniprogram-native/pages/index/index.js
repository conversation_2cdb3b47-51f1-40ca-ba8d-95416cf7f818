// pages/index/index.js
const app = getApp()

Page({
  data: {
    userInfo: {},
    todayTasks: 0,
    pendingSamples: 0,
    activeEquipment: 0,
    recentTasks: [],
    notifications: [],
    loading: true
  },

  onLoad() {
    console.log('首页加载')
    this.initPage()
  },

  onShow() {
    console.log('首页显示')
    // 检查登录状态
    if (!app.globalData.isLoggedIn) {
      wx.reLaunch({
        url: '/pages/login/login'
      })
      return
    }
    
    this.setData({
      userInfo: app.globalData.userInfo || {}
    })
    
    this.loadData()
  },

  onPullDownRefresh() {
    this.loadData().finally(() => {
      wx.stopPullDownRefresh()
    })
  },

  // 初始化页面
  initPage() {
    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: 'LIMS采样管理'
    })
  },

  // 加载数据
  async loadData() {
    this.setData({ loading: true })
    
    try {
      // 并行加载数据
      const [statsData, tasksData, notificationsData] = await Promise.all([
        this.loadStats(),
        this.loadRecentTasks(),
        this.loadNotifications()
      ])
      
      this.setData({
        todayTasks: statsData.todayTasks || 0,
        pendingSamples: statsData.pendingSamples || 0,
        activeEquipment: statsData.activeEquipment || 0,
        recentTasks: tasksData || [],
        notifications: notificationsData || []
      })
      
    } catch (error) {
      console.error('加载数据失败:', error)
      app.showToast('加载数据失败')
    } finally {
      this.setData({ loading: false })
    }
  },

  // 加载统计数据 - 返回模拟数据
  loadStats() {
    // TODO: 后端暂无统计接口，返回模拟数据
    console.log('统计数据功能待完善，当前返回模拟数据')
    return Promise.resolve({
      todayTasks: 5,
      pendingSamples: 12,
      activeEquipment: 8
    })
  },

  // 加载最近任务
  loadRecentTasks() {
    return app.request({
      url: '/sampling/task/page', // 使用现有的分页接口
      method: 'GET',
      data: { page: 1, size: 5 } // 获取前5条任务
    }).then(result => {
      if (result.code === 200) {
        const data = result.data || {}

        // 使用与任务列表相同的数据解析逻辑
        let taskList = []
        if (data.records && data.records.length > 0) {
          const firstRecord = data.records[0]
          if (firstRecord.taskName && !firstRecord.detectionParameter) {
            taskList = data.records.slice(0, 5) // 只取前5个
          } else if (firstRecord.detectionParameter) {
            const taskMap = new Map()
            data.records.forEach(item => {
              const taskId = item.projectQuotationId
              if (!taskMap.has(taskId)) {
                taskMap.set(taskId, {
                  id: taskId,
                  title: item.projectName || '未命名任务',
                  location: '多个点位',
                  status: 0,
                  completedSamples: 0,
                  totalSamples: 0,
                  createTime: item.createTime
                })
              }
              const task = taskMap.get(taskId)
              task.totalSamples++
            })
            taskList = Array.from(taskMap.values()).slice(0, 5) // 只取前5个
          }
        }

        return taskList.map(task => ({
          ...task,
          statusText: this.getStatusText(task.status),
          progress: Math.round((task.completedSamples / task.totalSamples) * 100) || 0,
          createTime: this.formatTime(task.createTime)
        }))
      } else {
        throw new Error(result.message || '获取最近任务失败')
      }
    }).catch(error => {
      console.error('加载最近任务失败:', error)
      // 返回模拟数据
      return [
        {
          id: 1,
          title: '水质采样任务-001',
          location: '北京市朝阳区',
          status: 'in_progress',
          statusText: '进行中',
          completedSamples: 3,
          totalSamples: 5,
          progress: 60,
          createTime: '2024-01-15 09:30'
        },
        {
          id: 2,
          title: '土壤采样任务-002',
          location: '北京市海淀区',
          status: 'pending',
          statusText: '待开始',
          completedSamples: 0,
          totalSamples: 8,
          progress: 0,
          createTime: '2024-01-15 10:00'
        }
      ]
    })
  },

  // 加载通知 - 返回模拟数据
  loadNotifications() {
    // TODO: 后端暂无通知接口，返回模拟数据
    console.log('通知功能待完善，当前返回模拟数据')
    return Promise.resolve([
      {
        id: 1,
        title: '系统维护通知',
        content: '系统将于今晚22:00-24:00进行维护，请提前保存数据',
        time: '2小时前'
      },
      {
        id: 2,
        title: '新任务分配',
        content: '您有新的采样任务需要处理，请及时查看',
        time: '5小时前'
      },
      {
        id: 3,
        title: '设备状态提醒',
        content: '检测设备A需要进行定期维护',
        time: '1天前'
      }
    ])
  },

  // 获取状态文本
  getStatusText(status) {
    const statusMap = {
      0: '待执行',
      1: '执行中',
      2: '已完成',
      3: '已取消',
      // 兼容字符串状态
      'pending': '待执行',
      'in_progress': '执行中',
      'completed': '已完成',
      'cancelled': '已取消'
    }
    return statusMap[status] || '未知'
  },

  // 格式化时间
  formatTime(timeStr) {
    if (!timeStr) return ''
    
    const time = new Date(timeStr)
    const now = new Date()
    const diff = now - time
    
    if (diff < 60000) { // 1分钟内
      return '刚刚'
    } else if (diff < 3600000) { // 1小时内
      return Math.floor(diff / 60000) + '分钟前'
    } else if (diff < 86400000) { // 24小时内
      return Math.floor(diff / 3600000) + '小时前'
    } else {
      return time.toLocaleDateString() + ' ' + time.toLocaleTimeString().slice(0, 5)
    }
  },

  // 扫码功能
  scanCode() {
    wx.scanCode({
      success: (res) => {
        console.log('扫码结果:', res)
        this.handleScanResult(res.result)
      },
      fail: (error) => {
        console.error('扫码失败:', error)
        app.showToast('扫码失败')
      }
    })
  },

  // 处理扫码结果
  handleScanResult(result) {
    try {
      // 尝试解析二维码内容
      const data = JSON.parse(result)
      
      if (data.type === 'equipment') {
        // 设备二维码
        wx.navigateTo({
          url: `/pages/equipment/detail?id=${data.id}`
        })
      } else if (data.type === 'task') {
        // 任务二维码
        wx.navigateTo({
          url: `/pages/sampling/task-detail?id=${data.id}`
        })
      } else {
        app.showToast('未识别的二维码类型')
      }
    } catch (error) {
      // 如果不是JSON格式，当作普通文本处理
      app.showToast(`扫码结果: ${result}`)
    }
  },

  // 跳转到任务列表
  goToTasks() {
    wx.switchTab({
      url: '/pages/sampling/task-list'
    })
  },

  // 跳转到设备管理
  goToEquipment() {
    wx.navigateTo({
      url: '/pages/equipment/detail'
    })
  },

  // 跳转到样品管理
  goToSamples() {
    wx.navigateTo({
      url: '/pages/sampling/sample-management'
    })
  },

  // 打开打印机配置
  openPrinterConfig() {
    wx.navigateTo({
      url: '/pages/printer/config'
    })
  },

  // 跳转到任务详情
  goToTaskDetail(e) {
    const taskId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/sampling/task-detail?id=${taskId}`
    })
  },

  // 分享功能
  onShareAppMessage() {
    return {
      title: 'LIMS实验室管理系统',
      path: '/pages/index/index',
      imageUrl: '/static/images/share-logo.png'
    }
  },

  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: 'LIMS实验室管理系统',
      query: '',
      imageUrl: '/static/images/share-logo.png'
    }
  }
})
