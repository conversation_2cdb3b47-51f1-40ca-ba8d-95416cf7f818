<!--pages/index/index.wxml-->
<view class="container">
  <!-- 头部欢迎区域 -->
  <view class="header-section">
    <view class="welcome-card card">
      <view class="user-info">
        <image class="avatar" src="{{userInfo.avatar || '/static/images/default-avatar.png'}}" mode="aspectFill"></image>
        <view class="user-details">
          <text class="username">{{userInfo.username || '未登录'}}</text>
          <text class="role">{{userInfo.role || '请先登录'}}</text>
        </view>
      </view>
      <view class="stats-row">
        <view class="stat-item">
          <text class="stat-number">{{todayTasks}}</text>
          <text class="stat-label">今日任务</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{pendingSamples}}</text>
          <text class="stat-label">待处理样品</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{activeEquipment}}</text>
          <text class="stat-label">在线设备</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 快捷功能区域 -->
  <view class="quick-actions">
    <view class="section-title">
      <text class="title-text">快捷功能</text>
    </view>
    <view class="actions-grid">
      <view class="action-item" bindtap="scanCode">
        <view class="action-icon scan-icon">
          <image src="/static/icons/scan.png" mode="aspectFit"></image>
        </view>
        <text class="action-text">扫码</text>
      </view>
      <view class="action-item" bindtap="goToTasks">
        <view class="action-icon task-icon">
          <image src="/static/icons/task.png" mode="aspectFit"></image>
        </view>
        <text class="action-text">采样任务</text>
      </view>
      <view class="action-item" bindtap="goToEquipment">
        <view class="action-icon equipment-icon">
          <image src="/static/icons/equipment.png" mode="aspectFit"></image>
        </view>
        <text class="action-text">设备管理</text>
      </view>
      <view class="action-item" bindtap="goToSamples">
        <view class="action-icon sample-icon">
          <image src="/static/icons/sample.png" mode="aspectFit"></image>
        </view>
        <text class="action-text">样品管理</text>
      </view>
      <view class="action-item" bindtap="openPrinterConfig">
        <view class="action-icon printer-icon">
          <image src="/static/icons/printer.png" mode="aspectFit"></image>
        </view>
        <text class="action-text">打印机配置</text>
      </view>
    </view>
  </view>

  <!-- 最近任务 -->
  <view class="recent-tasks">
    <view class="section-title">
      <text class="title-text">最近任务</text>
      <text class="more-text" bindtap="goToTasks">查看更多</text>
    </view>
    <view class="task-list">
      <view wx:for="{{recentTasks}}" wx:key="id" class="task-item card" bindtap="goToTaskDetail" data-id="{{item.id}}">
        <view class="task-header">
          <text class="task-title">{{item.title}}</text>
          <view class="task-status status-{{item.status}}">
            <text>{{item.statusText}}</text>
          </view>
        </view>
        <view class="task-info">
          <text class="task-location">📍 {{item.location}}</text>
          <text class="task-time">🕐 {{item.createTime}}</text>
        </view>
        <view class="task-progress">
          <text class="progress-text">进度: {{item.completedSamples}}/{{item.totalSamples}}</text>
          <view class="progress-bar">
            <view class="progress-fill" style="width: {{item.progress}}%"></view>
          </view>
        </view>
      </view>
      
      <!-- 空状态 -->
      <view wx:if="{{recentTasks.length === 0}}" class="empty-state">
        <image src="/static/images/empty-task.png" mode="aspectFit"></image>
        <text class="empty-text">暂无最近任务</text>
      </view>
    </view>
  </view>

  <!-- 系统通知 -->
  <view class="notifications" wx:if="{{notifications.length > 0}}">
    <view class="section-title">
      <text class="title-text">系统通知</text>
    </view>
    <view class="notification-list">
      <view wx:for="{{notifications}}" wx:key="id" class="notification-item card">
        <view class="notification-header">
          <text class="notification-title">{{item.title}}</text>
          <text class="notification-time">{{item.time}}</text>
        </view>
        <text class="notification-content">{{item.content}}</text>
      </view>
    </view>
  </view>
</view>

<!-- 加载状态 -->
<view wx:if="{{loading}}" class="loading-overlay">
  <view class="loading-content">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>
</view>
