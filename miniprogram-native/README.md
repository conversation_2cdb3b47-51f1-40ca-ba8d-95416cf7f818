# LIMS微信小程序（原生版本）

这是LIMS实验室信息管理系统的微信小程序原生版本，使用微信小程序原生开发框架构建。

## 🚀 快速开始

### 1. 环境准备
- 下载并安装[微信开发者工具](https://developers.weixin.qq.com/miniprogram/dev/devtools/download.html)
- 注册微信小程序账号并获取AppID

### 2. 项目配置
1. 打开微信开发者工具
2. 选择"导入项目"
3. 选择 `miniprogram-native` 目录
4. 输入你的小程序AppID（或使用测试号）
5. 点击"导入"

### 3. 配置后端API
在 `app.js` 中修改后端API地址：
```javascript
globalData: {
  baseURL: 'https://your-api-server.com', // 修改为你的后端地址
  // ...
}
```

### 4. 运行项目
1. 在微信开发者工具中点击"编译"
2. 在模拟器中预览效果
3. 点击"预览"生成二维码，在手机微信中扫码真机测试

## 📱 功能特性

### 已实现功能
- ✅ 用户登录/登出
- ✅ 首页数据展示
- ✅ 底部导航栏
- ✅ 响应式布局
- ✅ 错误处理
- ✅ 加载状态

### 待实现功能
- ⏳ 设备管理页面
- ⏳ 采样任务管理
- ⏳ 样品管理
- ⏳ 数据统计
- ⏳ 扫码功能
- ⏳ 个人中心

## 🏗️ 项目结构

```
miniprogram-native/
├── app.js              # 小程序入口文件
├── app.json            # 小程序配置文件
├── app.wxss            # 全局样式文件
├── sitemap.json        # 站点地图配置
├── project.config.json # 项目配置文件
├── pages/              # 页面目录
│   ├── index/          # 首页
│   │   ├── index.wxml  # 页面结构
│   │   ├── index.js    # 页面逻辑
│   │   └── index.wxss  # 页面样式
│   ├── login/          # 登录页
│   └── ...             # 其他页面
├── static/             # 静态资源
│   ├── images/         # 图片
│   ├── icons/          # 图标
│   └── tabbar/         # 导航图标
└── README.md           # 项目说明
```

## 🎨 设计规范

### 颜色规范
- 主色调：#409EFF（蓝色）
- 成功色：#67C23A（绿色）
- 警告色：#E6A23C（橙色）
- 危险色：#F56C6C（红色）
- 文字色：#303133（深灰）
- 辅助色：#909399（中灰）

### 字体规范
- 标题：32rpx - 40rpx
- 正文：28rpx
- 辅助文字：24rpx
- 小字：20rpx

### 间距规范
- 页面边距：20rpx
- 卡片内边距：30rpx
- 元素间距：20rpx
- 小间距：10rpx

## 🔧 开发指南

### 添加新页面
1. 在 `pages` 目录下创建新的页面目录
2. 创建 `.wxml`、`.js`、`.wxss` 文件
3. 在 `app.json` 的 `pages` 数组中添加页面路径

### 网络请求
使用 `app.request()` 方法进行网络请求：
```javascript
const app = getApp()

app.request({
  url: '/api/your-endpoint',
  method: 'GET',
  data: {}
}).then(result => {
  // 处理成功结果
}).catch(error => {
  // 处理错误
})
```

### 页面跳转
```javascript
// 跳转到新页面
wx.navigateTo({
  url: '/pages/target/target?param=value'
})

// 切换Tab页面
wx.switchTab({
  url: '/pages/index/index'
})

// 重定向
wx.redirectTo({
  url: '/pages/target/target'
})
```

### 数据存储
```javascript
// 存储数据
wx.setStorageSync('key', value)

// 获取数据
const value = wx.getStorageSync('key')

// 删除数据
wx.removeStorageSync('key')
```

## 🧪 测试

### 功能测试
1. 登录功能测试
2. 页面跳转测试
3. 数据加载测试
4. 错误处理测试

### 兼容性测试
- iOS设备测试
- Android设备测试
- 不同微信版本测试

## 📦 发布部署

### 1. 代码审查
- 检查代码质量
- 移除调试代码
- 优化性能

### 2. 配置检查
- 确认AppID正确
- 检查域名配置
- 验证权限设置

### 3. 上传代码
1. 在微信开发者工具中点击"上传"
2. 填写版本号和备注
3. 上传成功

### 4. 提交审核
1. 登录微信公众平台
2. 进入版本管理
3. 提交审核

## 🐛 常见问题

### Q: 网络请求失败
A: 检查以下几点：
1. 后端服务是否正常运行
2. 域名是否在微信公众平台配置
3. 是否使用HTTPS协议

### Q: 页面白屏
A: 检查以下几点：
1. 页面路径是否正确
2. 是否在app.json中注册页面
3. 查看控制台错误信息

### Q: 真机调试异常
A: 检查以下几点：
1. 手机微信版本是否支持
2. 是否开启调试模式
3. 网络连接是否正常

## 📞 技术支持

如有问题，请联系：
- 邮箱：<EMAIL>
- 电话：400-xxx-xxxx

## 📄 更新日志

### v1.0.0 (2024-01-15)
- 初始版本发布
- 实现基础登录功能
- 完成首页布局
- 添加底部导航

---

**注意**：这是原生微信小程序版本，如果你更熟悉uni-app或其他跨平台框架，建议使用对应的版本。
