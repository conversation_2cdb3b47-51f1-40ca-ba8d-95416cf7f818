# LIMS微信小程序使用说明

## 🎯 解决方案

针对您遇到的 "app.json: 在项目根目录未找到 app.json" 错误，我已经为您创建了一个**原生微信小程序版本**。

## 📁 项目结构

```
miniprogram-native/          # 原生微信小程序项目
├── app.js                   # ✅ 小程序入口文件
├── app.json                 # ✅ 小程序配置文件 (这就是微信开发者工具需要的)
├── app.wxss                 # ✅ 全局样式文件
├── sitemap.json             # ✅ 站点地图配置
├── project.config.json      # ✅ 项目配置文件
├── pages/                   # 页面目录
│   ├── index/               # 首页
│   ├── login/               # 登录页
│   └── sampling/            # 采样相关页面
├── static/                  # 静态资源
└── README.md                # 项目说明
```

## 🚀 立即使用

### 1. 打开微信开发者工具
- 启动微信开发者工具
- 选择"导入项目"

### 2. 导入项目
- **项目目录**: 选择 `miniprogram-native` 文件夹（不是miniprogram）
- **AppID**: 使用您的小程序AppID (`wx26d05ec5c4a66314`)
- **项目名称**: LIMS实验室管理系统

### 3. 点击导入
现在应该可以正常导入了，因为：
- ✅ `app.json` 文件存在
- ✅ 页面配置正确
- ✅ 项目结构符合微信小程序规范
- ✅ 已移除TabBar图标依赖（暂时无图标显示）

### 4. 图标说明
目前底部导航栏没有图标显示，这是正常的。如需添加图标：
1. 将图标文件放入 `static/tabbar/` 目录
2. 在 `app.json` 中恢复图标配置

## 🎨 已实现功能

### ✅ 完成的功能
1. **用户登录系统**
   - 用户名密码登录
   - 记住密码功能
   - 演示账号快速登录
   - 微信登录支持
   - 错误处理和提示

2. **首页展示**
   - 用户信息展示
   - 数据统计卡片
   - 快捷功能入口
   - 最近任务列表
   - 系统通知

3. **任务管理**
   - 任务列表展示
   - 搜索和筛选
   - 任务状态管理
   - 进度显示
   - 操作按钮

4. **界面设计**
   - 现代化UI设计
   - 响应式布局
   - 流畅动画效果
   - 统一的设计规范

### ⏳ 待完成功能
1. 设备管理页面
2. 采样任务详情
3. 样品管理
4. 数据统计图表
5. 扫码功能
6. 个人中心
7. 离线缓存

## 🔧 配置说明

### 1. 修改后端API地址
在 `app.js` 中修改：
```javascript
globalData: {
  baseURL: 'https://your-api-server.com', // 改为您的后端地址
  // ...
}
```

### 2. 配置小程序信息
在 `project.config.json` 中修改：
```json
{
  "appid": "your-miniprogram-appid", // 改为您的小程序AppID
  "projectname": "lims-miniprogram"
}
```

### 3. 添加静态资源
将图标和图片文件放入 `static/` 目录：
- `static/images/` - 图片资源
- `static/icons/` - 图标资源
- `static/tabbar/` - 底部导航图标

## 🧪 测试账号

### 演示账号
- 用户名: `admin`
- 密码: `admin123`

### 测试功能
1. 点击"演示账号"按钮自动填入
2. 或手动输入用户名密码
3. 登录成功后可以查看首页和任务列表

## 📱 页面说明

### 1. 登录页面 (`/pages/login/login`)
- 美观的渐变背景
- 用户名密码输入
- 记住密码功能
- 快速登录选项
- 错误提示弹窗

### 2. 首页 (`/pages/index/index`)
- 用户信息卡片
- 数据统计展示
- 快捷功能入口
- 最近任务列表
- 下拉刷新支持

### 3. 任务列表 (`/pages/sampling/task-list`)
- 搜索和筛选功能
- 任务状态统计
- 任务卡片展示
- 进度条显示
- 操作按钮

## 🎯 下一步开发

### 优先级1 - 核心功能
1. **任务详情页面** - 显示任务详细信息
2. **样品管理页面** - 样品录入和管理
3. **扫码功能** - 二维码扫描

### 优先级2 - 增强功能
1. **设备管理** - 设备状态监控
2. **数据统计** - 图表展示
3. **个人中心** - 用户设置

### 优先级3 - 高级功能
1. **离线缓存** - 离线数据支持
2. **消息推送** - 任务提醒
3. **文件上传** - 图片和文档

## 🐛 常见问题

### Q: 提示找不到TabBar图标文件？
A: **已解决**！我已经移除了TabBar的图标配置，现在可以正常运行：
1. 底部导航栏只显示文字，没有图标
2. 功能完全正常，只是视觉效果稍简单
3. 如需添加图标，请参考 `static/tabbar/README.md` 说明

### Q: 还是提示找不到app.json？
A: 请确保：
1. 导入的是 `miniprogram-native` 目录
2. 不是 `miniprogram` 目录
3. 目录中确实存在 `app.json` 文件

### Q: 页面显示异常？
A: 检查：
1. 静态资源文件是否存在
2. 图标路径是否正确
3. 控制台是否有错误信息

### Q: 网络请求失败？
A: 确认：
1. 后端服务是否启动
2. API地址是否正确
3. 域名是否在微信公众平台配置

### Q: 如何添加TabBar图标？
A: 按以下步骤操作：
1. 准备8个图标文件（81x81px PNG格式）
2. 放入 `static/tabbar/` 目录
3. 在 `app.json` 中恢复图标配置：
```json
{
  "pagePath": "pages/index/index",
  "iconPath": "static/tabbar/home.png",
  "selectedIconPath": "static/tabbar/home-active.png",
  "text": "首页"
}
```

## 📞 技术支持

如果您在使用过程中遇到任何问题：

1. **检查控制台** - 查看错误信息
2. **参考文档** - 阅读README.md
3. **联系支持** - 提供具体错误信息

---

**现在您可以直接在微信开发者工具中导入 `miniprogram-native` 目录，应该可以正常运行了！** 🎉
