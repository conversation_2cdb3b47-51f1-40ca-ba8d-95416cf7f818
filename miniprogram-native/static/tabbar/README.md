# TabBar 图标目录

这个目录用于存放底部导航栏的图标文件。

## 需要的图标文件

### 首页图标
- `home.png` - 首页未选中状态 (81x81px)
- `home-active.png` - 首页选中状态 (81x81px)

### 任务图标  
- `task.png` - 任务未选中状态 (81x81px)
- `task-active.png` - 任务选中状态 (81x81px)

### 统计图标
- `stats.png` - 统计未选中状态 (81x81px)  
- `stats-active.png` - 统计选中状态 (81x81px)

### 个人中心图标
- `profile.png` - 个人中心未选中状态 (81x81px)
- `profile-active.png` - 个人中心选中状态 (81x81px)

## 图标规范

1. **尺寸**: 81x81px (推荐)
2. **格式**: PNG格式，支持透明背景
3. **颜色**: 
   - 未选中: #7A7E83 (灰色)
   - 选中: #409EFF (蓝色)
4. **风格**: 简洁、清晰、易识别

## 临时解决方案

目前app.json中已经移除了图标配置，小程序可以正常运行。
如需添加图标，请：
1. 将图标文件放入此目录
2. 在app.json中恢复iconPath和selectedIconPath配置
