<template>
  <div class="h5-execution-tasks">
    <!-- 头部 -->
    <div class="header">
      <h2>执行任务</h2>
      <div class="header-actions">
        <el-button 
          type="primary" 
          size="small" 
          @click="showSearch = !showSearch"
          :icon="showSearch ? 'ArrowUp' : 'Search'"
        >
          {{ showSearch ? '收起' : '搜索' }}
        </el-button>
      </div>
    </div>

    <!-- 搜索区域 -->
    <div v-show="showSearch" class="search-section">
      <div class="search-form">
        <div class="form-item">
          <label>任务名称：</label>
          <el-input
            v-model="queryParams.taskName"
            placeholder="请输入任务名称"
            clearable
            size="small"
          />
        </div>
        <div class="form-item">
          <label>任务状态：</label>
          <el-select 
            v-model="queryParams.status" 
            placeholder="请选择状态" 
            clearable 
            size="small"
          >
            <el-option label="待执行" value="0" />
            <el-option label="执行中" value="1" />
            <el-option label="已完成" value="2" />
          </el-select>
        </div>
        <div class="form-actions">
          <el-button type="primary" size="small" @click="handleQuery">搜索</el-button>
          <el-button size="small" @click="resetQuery">重置</el-button>
        </div>
      </div>
    </div>

    <!-- 标题区域 -->
    <div class="title-section">
      <h3>我的执行任务</h3>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading">
      <el-skeleton :rows="5" animated />
    </div>

    <!-- 任务列表 -->
    <div v-else class="task-list">
      <div v-if="executionList.length === 0" class="empty-state">
        <el-icon><Document /></el-icon>
        <p>暂无执行任务</p>
        <p class="debug-info">调试信息：共 {{ total }} 条数据</p>
      </div>

      <div
        v-for="task in executionList"
        :key="task.id"
        class="task-card"
      >
        <!-- 任务头部 -->
        <div class="task-header" @click="toggleTaskExpand(task)">
          <div class="task-title">
            <div class="task-name">{{ task.taskName }}</div>
            <div class="task-code" v-if="task.taskCode">编号: {{ task.taskCode }}</div>
          </div>
          <div class="task-status" :class="getStatusClass(task.status)">
            {{ getStatusLabel(task.status) }}
          </div>
          <div class="expand-icon">
            <el-icon>
              <ArrowDown v-if="!expandedTasks.has(task.id)" />
              <ArrowUp v-else />
            </el-icon>
          </div>
        </div>

        <!-- 任务基本信息 -->
        <div class="task-info">
          <div class="info-row">
            <span class="label">项目：</span>
            <span class="value">{{ task.projectName || '-' }}</span>
          </div>
          <div class="info-row">
            <span class="label">客户：</span>
            <span class="value">{{ task.customerName || '-' }}</span>
          </div>
          <div class="info-row">
            <span class="label">负责人：</span>
            <span class="value">{{ task.responsibleUserName || '-' }}</span>
          </div>
          <div class="info-row">
            <span class="label">计划时间：</span>
            <span class="value">
              {{ formatDate(task.plannedStartDate) }} ~ {{ formatDate(task.plannedEndDate) }}
            </span>
          </div>
          <div class="info-row">
            <span class="label">分组数量：</span>
            <span class="value">{{ task.taskGroups ? task.taskGroups.length : 0 }} 个分组</span>
          </div>
        </div>

        <!-- 展开的分组列表 -->
        <div v-if="expandedTasks.has(task.id)" class="groups-section">
          <div class="groups-title">采样任务分组</div>
          <div v-if="task.taskGroups && task.taskGroups.length > 0" class="groups-list">
            <div
              v-for="group in task.taskGroups"
              :key="group.id"
              class="group-card"
              @click="handleGroupDetail(group)"
            >
              <div class="group-header">
                <div class="group-title">
                  <div class="group-name">{{ group.groupCode }}</div>
                </div>
                <div class="group-status" :class="getStatusClass(group.status)">
                  {{ getStatusLabel(group.status) }}
                </div>
              </div>
              <div class="group-info">
                <div class="info-row">
                  <span class="label">周期：</span>
                  <span class="value">{{ group.cycleNumber }} ({{ group.cycleType }})</span>
                </div>
                <div class="info-row">
                  <span class="label">类别：</span>
                  <span class="value">{{ group.detectionCategory || '-' }}</span>
                </div>
                <div class="info-row">
                  <span class="label">点位：</span>
                  <span class="value">{{ group.pointName || '-' }}</span>
                </div>
                <div class="info-row">
                  <span class="label">执行人：</span>
                  <span class="value">
                    <el-tag
                      v-for="name in group.assignedUserNames"
                      :key="name"
                      size="small"
                      style="margin-right: 4px;"
                    >
                      {{ name }}
                    </el-tag>
                  </span>
                </div>
              </div>
              <!-- 分组操作按钮 -->
              <div class="group-actions" @click.stop>
                <el-button
                  v-if="group.status === 0"
                  type="primary"
                  size="small"
                  @click="handleExecute(group)"
                >
                  开始执行
                </el-button>
                <el-button
                  v-if="group.status === 1"
                  type="success"
                  size="small"
                  @click="handleComplete(group)"
                >
                  完成任务
                </el-button>
                <el-button
                  v-if="group.status === 1"
                  type="warning"
                  size="small"
                  @click="handleSamplingManagement(group)"
                >
                  采样管理
                </el-button>
                <el-button
                  size="small"
                  @click="handleGroupDetail(group)"
                >
                  查看详情
                </el-button>
              </div>
            </div>
          </div>
          <div v-else class="no-groups">
            <p>该任务暂无分组</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 上拉加载更多 -->
    <div v-if="hasMore && !loading" class="load-more" @click="loadMore">
      <el-button type="text">加载更多</el-button>
    </div>

    <!-- 底部信息 -->
    <div class="footer">
      <p>共 {{ total }} 个任务</p>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance } from 'vue'
import { useRouter } from 'vue-router'
import { ArrowUp, ArrowDown, Search, Document } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { getUserGroups, updateGroupStatus, getTaskGroups } from '@/api/sampling/taskGroup'
import { getMyTasks } from '@/api/sampling/samplingTask'
import { parseTime } from '@/utils/ruoyi'
import { setCache, getCache, CACHE_KEYS } from '@/utils/cache'

const router = useRouter()
const { proxy } = getCurrentInstance()

// 响应式数据
const loading = ref(true)
const showSearch = ref(false)
const executionList = ref([])
const total = ref(0)
const hasMore = ref(false)
const expandedTasks = ref(new Set()) // 记录展开的任务ID

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  taskName: '',
  status: ''
})

// 方法
const getList = async (isLoadMore = false) => {
  try {
    if (!isLoadMore) {
      loading.value = true
      queryParams.value.pageNum = 1

      // 先尝试从缓存获取数据
      const cacheKey = `${CACHE_KEYS.TASK_DETAIL}execution_my_tasks`
      const cachedData = getCache(cacheKey)

      if (cachedData && Array.isArray(cachedData)) {
        executionList.value = cachedData
        total.value = cachedData.length
        loading.value = false
        console.log('使用缓存数据:', cachedData)

        // 后台更新数据
        try {
          const response = await getMyTasks()
          console.log('后台更新API响应:', response)

          if (response.code === 200) {
            let newData = response.data || response.rows || []
            if (typeof response.data === 'object' && response.data.rows) {
              newData = response.data.rows
            }
            if (Array.isArray(newData)) {
              // 为每个任务加载分组信息
              await loadTaskGroups(newData)
              executionList.value = newData
              total.value = response.total || newData.length
              setCache(cacheKey, newData)
            }
          }
        } catch (error) {
          console.log('后台更新失败，使用缓存数据')
        }
        return
      }
    }

    const response = await getMyTasks()
    console.log('API响应:', response)

    if (response.code === 200) {
      // 处理不同的数据结构
      let newData = response.data || response.rows || []

      // 如果data是对象且包含rows，则使用rows
      if (typeof response.data === 'object' && response.data.rows) {
        newData = response.data.rows
      }

      console.log('原始数据:', newData)
      console.log('数据类型:', typeof newData, '是否数组:', Array.isArray(newData))

      // 确保newData是数组
      if (!Array.isArray(newData)) {
        console.warn('数据不是数组格式:', newData)
        newData = []
      }

      // 为每个任务加载分组信息
      await loadTaskGroups(newData)

      // 根据查询条件过滤数据
      if (queryParams.value.taskName) {
        newData = newData.filter(item =>
          item.taskName && item.taskName.includes(queryParams.value.taskName)
        )
      }

      if (queryParams.value.status !== '') {
        newData = newData.filter(item =>
          item.status == queryParams.value.status
        )
      }

      console.log('过滤后数据:', newData)

      if (isLoadMore) {
        executionList.value = [...executionList.value, ...newData]
      } else {
        executionList.value = newData
      }

      total.value = response.total || newData.length
      hasMore.value = false // 暂时不支持分页

      // 缓存数据
      const cacheKey = `${CACHE_KEYS.TASK_DETAIL}execution_my_tasks`
      setCache(cacheKey, executionList.value)
    } else {
      console.error('API返回错误:', response)
      ElMessage.error(response.msg || '获取任务列表失败')
    }
  } catch (error) {
    console.error('获取任务列表失败:', error)
    ElMessage.error('获取任务列表失败')
  } finally {
    loading.value = false
  }
}

// 为任务加载分组信息（如果后端没有返回的话）
const loadTaskGroups = async (tasks) => {
  for (const task of tasks) {
    // 如果后端已经返回了taskGroups，就不需要再次加载
    if (task.taskGroups && task.taskGroups.length > 0) {
      continue
    }

    try {
      const groupsResponse = await getTaskGroups(task.id)
      if (groupsResponse.code === 200) {
        task.taskGroups = groupsResponse.data || []
      } else {
        task.taskGroups = []
      }
    } catch (error) {
      console.error(`加载任务 ${task.id} 的分组失败:`, error)
      task.taskGroups = []
    }
  }
}

// 切换任务展开状态
const toggleTaskExpand = async (task) => {
  if (expandedTasks.value.has(task.id)) {
    expandedTasks.value.delete(task.id)
  } else {
    expandedTasks.value.add(task.id)
    // 如果还没有加载分组信息，则加载
    if (!task.taskGroups || task.taskGroups.length === 0) {
      try {
        const groupsResponse = await getTaskGroups(task.id)
        if (groupsResponse.code === 200) {
          task.taskGroups = groupsResponse.data || []
        }
      } catch (error) {
        console.error(`加载任务 ${task.id} 的分组失败:`, error)
        task.taskGroups = []
      }
    }
  }
}

const handleQuery = () => {
  getList()
}

const resetQuery = () => {
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    taskName: '',
    status: ''
  }
  getList()
}

const loadMore = () => {
  queryParams.value.pageNum++
  getList(true)
}

const handleExecute = async (task) => {
  try {
    await updateGroupStatus(task.id, 1)
    ElMessage.success('任务已开始执行')
    getList()
  } catch (error) {
    console.error('开始执行任务失败:', error)
    ElMessage.error('开始执行任务失败')
  }
}

const handleComplete = async (task) => {
  try {
    await updateGroupStatus(task.id, 2)
    ElMessage.success('任务已完成')
    getList()
  } catch (error) {
    console.error('完成任务失败:', error)
    ElMessage.error('完成任务失败')
  }
}

const handleSamplingManagement = (group) => {
  router.push(`/sampling-task-h5?id=${group.id}`)
}

const handleTaskDetail = (task) => {
  // 对于采样任务，可以跳转到任务详情页面或者展开显示分组
  toggleTaskExpand(task)
}

const handleGroupDetail = (group) => {
  router.push(`/task-detail-h5?id=${group.id}`)
}

const getStatusClass = (status) => {
  switch (status) {
    case 0: return 'status-pending'
    case 1: return 'status-active'
    case 2: return 'status-completed'
    default: return ''
  }
}

const getStatusLabel = (status) => {
  switch (status) {
    case 0: return '待执行'
    case 1: return '执行中'
    case 2: return '已完成'
    default: return '未知'
  }
}

const formatDate = (date) => {
  if (!date) return '-'
  return parseTime(date, '{y}-{m}-{d}')
}

// 生命周期
onMounted(() => {
  getList()
})
</script>

<style scoped>
.h5-execution-tasks {
  min-height: 100vh;
  background: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.header {
  background: linear-gradient(135deg, #409EFF, #67C23A);
  color: white;
  padding: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
}

.search-section {
  background: white;
  padding: 15px;
  border-bottom: 1px solid #e9ecef;
}

.search-form {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.form-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.form-item label {
  min-width: 70px;
  font-size: 14px;
  color: #666;
}

.form-item .el-input,
.form-item .el-select {
  flex: 1;
}

.form-actions {
  display: flex;
  gap: 8px;
  justify-content: center;
  margin-top: 8px;
}

.title-section {
  background: white;
  border-bottom: 1px solid #e9ecef;
  padding: 15px;
}

.title-section h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.loading {
  padding: 20px;
}

.task-list {
  padding: 15px;
}

.empty-state {
  padding: 60px 20px;
  text-align: center;
  color: #999;
}

.empty-state .el-icon {
  font-size: 48px;
  margin-bottom: 12px;
}

.debug-info {
  font-size: 12px;
  color: #999;
  margin-top: 8px;
}

.task-card {
  background: white;
  border-radius: 8px;
  margin-bottom: 15px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: transform 0.2s;
}

.task-card:hover {
  transform: translateY(-2px);
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  cursor: pointer;
}

.task-title {
  flex: 1;
}

.task-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.task-code {
  font-size: 12px;
  color: #409eff;
  margin-top: 4px;
  font-weight: 500;
}

.expand-icon {
  margin-left: 10px;
  color: #666;
}

.task-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.status-pending {
  background: #fff7e6;
  color: #fa8c16;
}

.status-active {
  background: #f0f9ff;
  color: #1890ff;
}

.status-completed {
  background: #f6ffed;
  color: #52c41a;
}

.task-info {
  padding: 15px;
}

.info-row {
  display: flex;
  margin-bottom: 8px;
  font-size: 14px;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-row .label {
  color: #666;
  min-width: 60px;
  margin-right: 8px;
}

.info-row .value {
  color: #333;
  word-break: break-all;
  flex: 1;
}

.task-actions {
  padding: 12px 15px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.load-more {
  text-align: center;
  padding: 20px;
}

.footer {
  text-align: center;
  padding: 20px;
  color: #999;
  font-size: 12px;
  background: white;
  margin-top: 15px;
}

.footer p {
  margin: 0;
}

/* 分组相关样式 */
.groups-section {
  border-top: 1px solid #e9ecef;
  background: #fafafa;
}

.groups-title {
  padding: 12px 15px;
  font-size: 14px;
  font-weight: 600;
  color: #666;
  background: #f0f0f0;
  border-bottom: 1px solid #e9ecef;
}

.groups-list {
  padding: 10px;
}

.group-card {
  background: white;
  border-radius: 6px;
  margin-bottom: 10px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: transform 0.2s;
}

.group-card:hover {
  transform: translateY(-1px);
}

.group-card:last-child {
  margin-bottom: 0;
}

.group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.group-title {
  flex: 1;
}

.group-name {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.group-status {
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 11px;
}

.group-info {
  padding: 12px;
}

.group-info .info-row {
  margin-bottom: 6px;
  font-size: 13px;
}

.group-info .info-row:last-child {
  margin-bottom: 0;
}

.group-actions {
  padding: 10px 12px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.group-actions .el-button {
  font-size: 12px;
  padding: 4px 8px;
}

.no-groups {
  padding: 20px;
  text-align: center;
  color: #999;
  font-size: 14px;
}
</style>
