<template>
  <div class="contract-business">
    <!-- 查询条件 -->
    <el-form v-if="!readonly" :model="queryParams" :inline="true" class="query-form">
      <el-form-item label="项目编号">
        <el-input 
          v-model="queryParams.projectCode" 
          placeholder="请输入项目编号"
          clearable
          style="width: 200px"
        />
      </el-form-item>
      <el-form-item label="部门">
        <DeptTreeSelect 
          v-model="queryParams.deptId" 
          placeholder="请选择部门"
          style="width: 200px"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery">
          <el-icon><Search /></el-icon>
          搜索
        </el-button>
        <el-button @click="resetQuery">
          <el-icon><Refresh /></el-icon>
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 部门分摊信息 -->
    <el-divider content-position="left">部门分摊信息</el-divider>
    <div class="table-container">
      <div v-if="!readonly" class="table-header">
        <el-button type="primary" size="small" @click="addDepartment">
          <el-icon><Plus /></el-icon>
          添加部门分摊
        </el-button>
      </div>
      
      <el-table :data="displayDepartments" border style="width: 100%" size="small">
        <el-table-column type="index" label="序号" width="60" align="center" />
        <el-table-column label="项目编号" prop="projectCode" min-width="300">
          <template #default="{ row }">
            <el-select
              v-if="!readonly"
              v-model="row.projectCode"
              placeholder="请选择项目编号"
              filterable
              size="small"
              style="width: 100%"
              @change="handleProjectCodeChange(row)"
            >
              <el-option
                v-for="item in quotationList"
                :key="item.projectCode"
                :label="`${item.projectCode} - ${item.projectName}`"
                :value="item.projectCode"
              >
                <div style="display: flex; justify-content: space-between; align-items: center;">
                  <span>{{ item.projectCode }} - {{ item.projectName }}</span>
                  <span style="color: #409EFF; font-weight: bold; margin-left: 10px;">
                    ¥{{ (item.totalFee?.totalAmount || 0).toLocaleString() }}
                  </span>
                </div>
              </el-option>
            </el-select>
            <span v-else>{{ row.projectCode || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="部门" prop="deptId" min-width="200">
          <template #default="{ row }">
            <DeptTreeSelect
              v-if="!readonly"
              v-model="row.deptId"
              placeholder="请选择部门"
              @change="handleDeptChange(row)"
              size="small"
            />
            <span v-else>{{ row.deptName || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="分摊金额" prop="allocationAmount" min-width="100">
          <template #default="{ row }">
            <el-input-number
              v-if="!readonly"
              v-model="row.allocationAmount"
              :precision="2"
              :min="0"
              placeholder="分摊金额"
              size="small"
              style="width: 100%"
            />
            <span v-else>{{ row.allocationAmount ? '¥' + Number(row.allocationAmount).toLocaleString() : '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column v-if="!readonly" label="操作" width="100" align="center" fixed="right">
          <template #default="{ $index }">
            <el-button
              type="danger"
              size="small"
              @click="removeDepartment($index)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 任务拆解信息 -->
    <el-divider content-position="left">任务拆解信息</el-divider>
    <div class="table-container">
      <div v-if="!readonly" class="table-header">
        <el-button type="primary" size="small" @click="addTask">
          <el-icon><Plus /></el-icon>
          添加任务拆解
        </el-button>
      </div>
      
      <el-table :data="displayTasks" border style="width: 100%" size="small">
        <el-table-column type="index" label="序号" width="60" align="center" />
        <el-table-column label="项目编号" prop="projectCode" min-width="200">
          <template #default="{ row }">
            <el-select
              v-if="!readonly"
              v-model="row.projectCode"
              placeholder="请选择项目编号"
              filterable
              size="small"
              style="width: 100%"
              @change="handleProjectCodeChange(row)"
            >
              <el-option
                v-for="item in quotationList"
                :key="item.projectCode"
                :label="`${item.projectCode} - ${item.projectName}`"
                :value="item.projectCode"
              >
                <div style="display: flex; justify-content: space-between; align-items: center;">
                  <span>{{ item.projectCode }} - {{ item.projectName }}</span>
                  <span style="color: #409EFF; font-weight: bold; margin-left: 10px;">
                    ¥{{ (item.totalFee?.totalAmount || 0).toLocaleString() }}
                  </span>
                </div>
              </el-option>
            </el-select>
            <span v-else>{{ row.projectCode || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="任务编号" prop="taskCode" min-width="200">
          <template #default="{ row }">
            <el-select
              v-if="!readonly"
              v-model="row.taskCode"
              placeholder="请选择任务编号"
              filterable
              size="small"
              style="width: 100%"
              @change="handleTaskCodeChange(row)"
            >
              <el-option
                v-for="item in taskList"
                :key="item.taskCode"
                :label="`${item.taskCode} - ${item.taskName}`"
                :value="item.taskCode"
              />
            </el-select>
            <span v-else>{{ row.taskCode || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="任务金额" prop="taskAmount" min-width="100">
          <template #default="{ row }">
            <el-input-number
              v-if="!readonly"
              v-model="row.taskAmount"
              :precision="2"
              :min="0"
              placeholder="任务金额"
              size="small"
              style="width: 100%"
            />
            <span v-else>{{ row.taskAmount ? '¥' + Number(row.taskAmount).toLocaleString() : '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column v-if="!readonly" label="操作" width="100" align="center" fixed="right">
          <template #default="{ $index }">
            <el-button
              type="danger"
              size="small"
              @click="removeTask($index)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 保存按钮 -->
    <div v-if="!readonly" style="text-align: center; margin-top: 20px;">
      <el-button type="success" @click="saveBusinessInfo" :loading="saving">
        <el-icon><Check /></el-icon>
        保存商务信息
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { getContractBusiness, saveContractBusiness } from '@/api/contract/contract'
import { listDept } from '@/api/system/dept'
import { listContractQuotationRelation } from '@/api/contract/contractQuotationRelation'
import { getProjectQuotationFeeCalculation } from '@/api/quotation/projectQuotation'
import { pageSamplingTask } from '@/api/sampling/samplingTask'
import DeptTreeSelect from '@/components/DeptTreeSelect/index.vue'

const props = defineProps({
  contractId: {
    type: Number,
    default: null
  },
  readonly: {
    type: Boolean,
    default: false
  }
})

// 查询参数
const queryParams = ref({
  projectCode: '',
  deptId: null
})

// 保存状态
const saving = ref(false)

// 商务信息数据
const businessData = ref({
  departments: [],
  tasks: []
})

// 部门列表
const deptList = ref([])
// 项目报价列表
const quotationList = ref([])
// 任务列表
const taskList = ref([])

// 显示的部门分摊数据（应用搜索过滤）
const displayDepartments = ref([])
// 显示的任务数据（应用搜索过滤）
const displayTasks = ref([])

// 应用搜索过滤
const applyFilter = () => {
  let departments = [...(businessData.value.departments || [])]
  let tasks = [...(businessData.value.tasks || [])]

  if (queryParams.value.projectCode) {
    departments = departments.filter(dept =>
      dept.projectCode && dept.projectCode.includes(queryParams.value.projectCode)
    )
    tasks = tasks.filter(task =>
      task.projectCode && task.projectCode.includes(queryParams.value.projectCode)
    )
  }

  if (queryParams.value.deptId) {
    departments = departments.filter(dept => dept.deptId === queryParams.value.deptId)
  }
  console.log("applyFilter:", queryParams.value)
  displayDepartments.value = departments
  displayTasks.value = tasks
}

// 查询
const handleQuery = () => {
  applyFilter()
}

// 重置查询
const resetQuery = () => {
  queryParams.value = {
    projectCode: '',
    deptId: null
  }
  applyFilter()
}

// 添加部门分摊
const addDepartment = () => {
  businessData.value.departments.push({
    id: null,
    contractId: props.contractId,
    projectCode: '',
    projectName: '',
    deptId: null,
    deptName: '',
    allocationAmount: 0,
    quotationTotalAmount: 0
  })
  applyFilter() // 添加后重新应用过滤
}

// 删除部门分摊
const removeDepartment = (index) => {
  businessData.value.departments.splice(index, 1)
  applyFilter() // 删除后重新应用过滤
}

// 部门变更处理
const handleDeptChange = (row) => {
  const dept = deptList.value.find(d => d.deptId === row.deptId)
  if (dept) {
    row.deptName = dept.deptName
  }
}

// 项目编号变更处理
const handleProjectCodeChange = async (row) => {
  const quotation = quotationList.value.find(q => q.projectCode === row.projectCode)
  if (quotation) {
    row.projectName = quotation.projectName

    // 从费用计算接口获取优惠后总金额
    try {
      const feeResult = await getProjectQuotationFeeCalculation(quotation.id)
      if (feeResult.code === 200) {
        // 计算优惠后总金额，参考 EditQuotationDialog.vue 的 calculateTotalFee 函数
        const feeData = feeResult.data
        const discountedTestingFee = (feeData.testingFee * (feeData.discountRate || 100) / 100)
        const totalFeeBeforeDiscount = discountedTestingFee + (feeData.specialConsumablesFee || 0) + (feeData.otherFee || 0)
        const tax = totalFeeBeforeDiscount * (feeData.taxRate || 0) / 100
        const totalFeeAfterTax = totalFeeBeforeDiscount + tax
        const finalAmount = totalFeeAfterTax + (feeData.adjustmentAmount || 0)

        row.quotationTotalAmount = finalAmount
      } else {
        row.quotationTotalAmount = 0
      }
    } catch (error) {
      console.error('获取项目报价费用计算详情失败:', error)
      row.quotationTotalAmount = 0
    }
  }
}

// 任务编号变更处理
const handleTaskCodeChange = (row) => {
  const task = taskList.value.find(t => t.taskCode === row.taskCode)
  if (task) {
    row.taskName = task.taskName
  }
}

// 添加任务
const addTask = () => {
  businessData.value.tasks.push({
    id: null,
    contractId: props.contractId,
    projectCode: '',
    taskCode: '',
    taskName: '',
    taskAmount: 0
  })
  applyFilter() // 添加后重新应用过滤
}

// 删除任务
const removeTask = (index) => {
  businessData.value.tasks.splice(index, 1)
  applyFilter() // 删除后重新应用过滤
}

// 保存商务信息
const saveBusinessInfo = async () => {
  try {
    saving.value = true
    
    // 验证必填项
    for (const dept of businessData.value.departments) {
      if (!dept.projectCode) {
        ElMessage.error('项目编号不能为空')
        return
      }
      if (!dept.deptId) {
        ElMessage.error('部门不能为空')
        return
      }
    }
    
    for (const task of businessData.value.tasks) {
      if (!task.projectCode) {
        ElMessage.error('项目编号不能为空')
        return
      }
      if (!task.taskCode) {
        ElMessage.error('任务编号不能为空')
        return
      }
    }
    
    const result = await saveContractBusiness(businessData.value)
    if (result.code === 200) {
      ElMessage.success('商务信息保存成功')
      // 重新加载数据
      await loadBusinessData()
    } else {
      ElMessage.error(result.msg || '保存失败')
    }
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

// 加载商务信息数据
const loadBusinessData = async () => {
  if (!props.contractId) return

  try {
    const result = await getContractBusiness(props.contractId)
    if (result.code === 200) {
      businessData.value = result.data || { departments: [], tasks: [] }
      applyFilter() // 加载数据后应用过滤
    }
  } catch (error) {
    console.error('加载商务信息失败:', error)
  }
}

// 加载部门列表
const loadDeptList = async () => {
  try {
    const result = await listDept()
    if (result.code === 200) {
      deptList.value = result.data || []
    }
  } catch (error) {
    console.error('加载部门列表失败:', error)
  }
}

// 加载项目报价列表（从关联报价单获取）
const loadQuotationList = async () => {
  if (!props.contractId) {
    quotationList.value = []
    return
  }

  try {
    const result = await listContractQuotationRelation(props.contractId)
    if (result.code === 200) {
      quotationList.value = result.data.rows || []
    }
  } catch (error) {
    console.error('加载关联报价单列表失败:', error)
    quotationList.value = []
  }
}

// 加载任务列表
const loadTaskList = async () => {
  try {
    const result = await pageSamplingTask({ pageNum: 1, pageSize: 1000 })
    if (result.code === 200) {
      taskList.value = result.data?.records || []
    }
  } catch (error) {
    console.error('加载任务列表失败:', error)
  }
}

// 监听合同ID变化
watch(() => props.contractId, (newVal) => {
  if (newVal) {
    loadBusinessData()
    loadQuotationList() // 重新加载关联报价单列表
  }
}, { immediate: true })

onMounted(() => {
  loadDeptList()
  loadTaskList()
  // loadQuotationList() 会在 watch 中调用
})
</script>

<style scoped>
.contract-business {
  padding: 20px;
}

.query-form {
  background: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.table-container {
  margin-bottom: 20px;
}

.table-header {
  margin-bottom: 10px;
}
</style>
