<template>
  <el-dialog
    title="选择项目报价"
    v-model="visible"
    width="1200px"
    append-to-body
    :destroy-on-close="true"
    @close="handleClose"
  >
    <!-- 查询条件 -->
    <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="80px">
      <el-form-item label="项目编号" prop="projectCode">
        <el-input
          v-model="queryParams.projectCode"
          placeholder="请输入项目编号"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="项目名称" prop="projectName">
        <el-input
          v-model="queryParams.projectName"
          placeholder="请输入项目名称"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="客户名称" prop="customerName">
        <el-input
          v-model="queryParams.customerName"
          placeholder="请输入客户名称"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 数据表格 -->
    <el-table
      v-loading="loading"
      :data="projectList"
      @selection-change="handleSelectionChange"
      style="width: 100%"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="项目编号" align="center" prop="projectCode" min-width="120" show-overflow-tooltip />
      <el-table-column label="项目名称" align="center" prop="projectName" min-width="180" show-overflow-tooltip />
      <el-table-column label="客户名称" align="center" prop="customerName" min-width="150" show-overflow-tooltip />
      <el-table-column label="项目负责人" align="center" prop="projectManager" min-width="100" show-overflow-tooltip />
      <el-table-column label="项目状态" align="center" prop="status" min-width="100">
        <template #default="scope">
          <el-tag :type="getStatusType(scope.row.status)">{{ scope.row.status }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" min-width="150" />
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 底部按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleConfirm" :disabled="selectedProjects.length === 0">
          确 定 (已选择 {{ selectedProjects.length }} 项)
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch } from 'vue'
import { pageProjectQuotation } from '@/api/quotation/projectQuotation'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  excludeProjectCodes: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:modelValue', 'confirm'])

// 显示状态
const visible = ref(false)

// 查询参数
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  projectCode: '',
  projectName: '',
  customerName: ''
})

// 数据
const loading = ref(false)
const projectList = ref([])
const total = ref(0)
const selectedProjects = ref([])

// 监听显示状态
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
  if (newVal) {
    resetQuery()
  }
})

watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

/** 查询项目报价列表 */
const getList = async () => {
  loading.value = true
  try {
    const response = await pageProjectQuotation(queryParams.value)
    if (response.code === 200) {
      // 过滤掉已排除的项目
      const filteredRows = response.data?.rows?.filter(item => 
        !props.excludeProjectCodes.includes(item.projectCode)
      )
      projectList.value = filteredRows
      total.value = response.data?.total || 0
    }
  } catch (error) {
    console.error('查询项目报价列表失败:', error)
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    projectCode: '',
    projectName: '',
    customerName: ''
  }
  selectedProjects.value = []
  handleQuery()
}

/** 多选框选中数据 */
const handleSelectionChange = (selection) => {
  selectedProjects.value = selection
}

/** 获取状态类型 */
const getStatusType = (status) => {
  switch (status) {
    case '草稿':
      return 'info'
    case '待审核':
      return 'warning'
    case '审核中':
      return 'warning'
    case '已审核':
      return 'success'
    case '已完成':
      return 'success'
    case '已取消':
      return 'danger'
    default:
      return 'info'
  }
}

/** 确认选择 */
const handleConfirm = () => {
  if (selectedProjects.value.length === 0) {
    return
  }
  emit('confirm', selectedProjects.value)
  handleClose()
}

/** 关闭弹框 */
const handleClose = () => {
  visible.value = false
  selectedProjects.value = []
}
</script>

<style scoped>
.dialog-footer {
  text-align: center;
}
</style>
