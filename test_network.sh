#!/bin/bash

# LIMS系统网络连接测试脚本
echo "🔍 LIMS系统网络连接测试"
echo "=========================="

# 获取本机IP地址
LOCAL_IP=$(ip route get 1 | awk '{print $7; exit}')
echo "🌐 本机IP地址: $LOCAL_IP"

# 检查端口是否开放
echo ""
echo "📡 检查端口状态..."

# 检查9099端口（后端）
if ss -tlnp | grep -q ":9099"; then
    echo "✅ 后端端口 9099 正在监听"
else
    echo "❌ 后端端口 9099 未启动"
fi

# 检查4000端口（前端）
if ss -tlnp | grep -q ":4000"; then
    echo "✅ 前端端口 4000 正在监听"
else
    echo "❌ 前端端口 4000 未启动"
fi

# 测试后端API
echo ""
echo "🧪 测试后端API连接..."
if curl -s -o /dev/null -w "%{http_code}" "http://127.0.0.1:9099/dev-api/captchaImage" | grep -q "200"; then
    echo "✅ 本地后端API连接正常"
else
    echo "❌ 本地后端API连接失败"
fi

if curl -s -o /dev/null -w "%{http_code}" "http://$LOCAL_IP:9099/dev-api/captchaImage" | grep -q "200"; then
    echo "✅ 局域网后端API连接正常"
else
    echo "❌ 局域网后端API连接失败"
fi

# 显示访问地址
echo ""
echo "🔗 系统访问地址："
echo "📍 后端API文档: http://$LOCAL_IP:9099/dev-api/docs"
echo "📍 前端管理系统: http://$LOCAL_IP:4000"
echo "📍 小程序开发工具: 配置baseURL为 http://$LOCAL_IP:9099"

# 显示防火墙状态
echo ""
echo "🔥 防火墙检查..."
if command -v ufw &> /dev/null; then
    if ufw status | grep -q "Status: active"; then
        echo "⚠️  UFW防火墙已启用，请确保允许端口9099和4000"
        echo "   执行: sudo ufw allow 9099 && sudo ufw allow 4000"
    else
        echo "✅ UFW防火墙未启用"
    fi
elif command -v firewall-cmd &> /dev/null; then
    if firewall-cmd --state 2>/dev/null | grep -q "running"; then
        echo "⚠️  firewalld防火墙已启用，请确保允许端口9099和4000"
        echo "   执行: sudo firewall-cmd --permanent --add-port=9099/tcp --add-port=4000/tcp && sudo firewall-cmd --reload"
    else
        echo "✅ firewalld防火墙未启用"
    fi
else
    echo "ℹ️  未检测到常见防火墙工具"
fi

echo ""
echo "📋 局域网设备访问步骤："
echo "1. 确保设备连接到同一局域网"
echo "2. 在设备浏览器中访问: http://$LOCAL_IP:4000"
echo "3. 小程序开发工具中配置API地址: http://$LOCAL_IP:9099"
echo "4. 勾选'不校验合法域名'选项"

echo ""
echo "✨ 测试完成！"
