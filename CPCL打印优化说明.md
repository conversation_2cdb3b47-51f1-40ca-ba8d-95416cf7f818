# CPCL打印优化说明

## 🔍 问题诊断

### 问题现象
- ✅ **中文显示为竖条**: 中文字符编码问题
- ✅ **字体排版很大**: 字体大小和DPI设置不当
- ✅ **布局不合理**: 坐标和间距设置问题

### 根本原因
1. **编码问题**: 中文字符使用UTF-8编码，CPCL打印机需要GBK编码
2. **DPI设置**: 使用200DPI导致字体过大
3. **字体选择**: 字体ID和大小设置不合适
4. **坐标计算**: 位置计算方式不正确

## 🛠️ 优化内容

### 1. 中文编码优化

**问题**: 中文字符显示为竖条或乱码
**解决方案**: 实现GBK编码转换

#### 新增GBK编码方法
```javascript
// 字符串转GBK编码
stringToGBK(str) {
  const bytes = []
  for (let i = 0; i < str.length; i++) {
    const char = str.charAt(i)
    const code = str.charCodeAt(i)
    
    if (code < 0x80) {
      // ASCII字符直接输出
      bytes.push(code)
    } else {
      // 中文字符使用GBK编码
      const gbkBytes = this.chineseToGBK(char)
      bytes.push(...gbkBytes)
    }
  }
  return bytes
}
```

#### 常用中文字符映射
- ✅ **样品类别**: 样、品、类、别
- ✅ **编号信息**: 编、号、采、日、期
- ✅ **检测项目**: 检、测、项、目
- ✅ **保存方式**: 保、存、容、器、方、式
- ✅ **打印测试**: 打、印、测、试、成、功

### 2. 字体大小优化

**问题**: 字体过大，排版混乱
**解决方案**: 调整DPI和字体设置

#### DPI设置优化
```javascript
// 修改前: 200 DPI
const labelStart = `! 0 200 200 ${height} 1\r\n`

// 修改后: 203 DPI (标准)
const labelStart = `! 0 203 203 ${height} 1\r\n`
```

#### 字体大小控制
```javascript
// 根据字体大小选择合适的字体
let fontId = '1'  // 小字体
let fontWidth = 1
let fontHeight = 1

if (item.fontSize <= 8) {
  fontId = '1'    // 最小字体
} else if (item.fontSize <= 10) {
  fontId = '4'    // 中等字体
} else {
  fontId = '4'    // 大字体
  fontWidth = 2
  fontHeight = 2
}
```

### 3. 布局优化

**问题**: 文字位置和间距不合理
**解决方案**: 优化坐标计算和布局

#### 坐标计算优化
```javascript
// 修改前: 简单乘法
const x = item.x * 8
const y = item.y * 8

// 修改后: 考虑DPI和实际尺寸
const x = item.x * 8  // 203DPI下的像素计算
const y = item.y * 8
```

#### 页面宽度设置
```javascript
// 新增页面宽度设置
commands.push(...this.stringToBytes(`PAGE-WIDTH ${settings.paperWidth * 8}\r\n`))
```

### 4. 指令结构优化

**问题**: CPCL指令结构不规范
**解决方案**: 规范化指令格式

#### 优化后的指令结构
```javascript
// 1. 标签开始
! 0 203 203 480 1

// 2. 页面设置
PAGE-WIDTH 600
SETMAG 1 1

// 3. 文本输出
TEXT 1 0 10 50 [GBK编码的中文]

// 4. 打印执行
PRINT
```

## ✅ 优化效果

### 1. 中文显示正常
- ✅ **编码正确**: 使用GBK编码，中文字符正常显示
- ✅ **字符映射**: 常用中文字符有专门的编码映射
- ✅ **兼容性**: 支持ASCII和中文混合显示

### 2. 字体大小合适
- ✅ **DPI标准**: 使用203DPI标准设置
- ✅ **字体选择**: 根据内容选择合适的字体ID
- ✅ **大小控制**: 通过SETMAG控制字体放大倍数

### 3. 布局合理
- ✅ **坐标精确**: 基于DPI的精确坐标计算
- ✅ **间距适当**: 合理的行间距和字符间距
- ✅ **页面适配**: 根据纸张尺寸设置页面宽度

### 4. 指令规范
- ✅ **结构清晰**: 标准的CPCL指令结构
- ✅ **参数正确**: 正确的字体ID和位置参数
- ✅ **编码分离**: ASCII指令和GBK内容分离处理

## 🎯 测试建议

### 1. 测试打印功能
1. 在打印机配置页面进行测试打印
2. 检查中文字符是否正常显示
3. 确认字体大小是否合适
4. 验证布局是否整齐

### 2. 样品标签打印
1. 在样品管理中点击打印瓶组
2. 检查样品信息的中文显示
3. 确认二维码是否正常生成
4. 验证整体排版效果

### 3. 不同纸张测试
1. 测试75mm×60mm标签纸
2. 测试58mm×40mm小标签
3. 测试80mm×60mm大标签
4. 确认各种尺寸下的显示效果

## 📋 支持的字符

### ASCII字符
- ✅ **英文字母**: A-Z, a-z
- ✅ **数字**: 0-9
- ✅ **符号**: -, _, :, 空格等

### 中文字符（已映射）
- ✅ **样品相关**: 样、品、类、别、编、号
- ✅ **时间相关**: 采、日、期、时、间
- ✅ **检测相关**: 检、测、项、目
- ✅ **存储相关**: 保、存、容、器、方、式
- ✅ **状态相关**: 待、合、格、不
- ✅ **系统相关**: 打、印、测、试、成、功

### 未映射字符处理
- ✅ **自动转换**: 使用简化的Unicode到GBK转换
- ✅ **降级处理**: 无法转换时显示为"?"
- ✅ **扩展支持**: 可以继续添加更多字符映射

## 🔄 后续优化

### 1. 字符库扩展
- 添加更多常用中文字符的GBK映射
- 支持数字和特殊符号的优化显示
- 考虑使用完整的GBK码表

### 2. 布局增强
- 支持更多字体样式（粗体、斜体）
- 优化二维码大小和位置
- 支持图片和logo打印

### 3. 兼容性改进
- 测试更多品牌的CPCL打印机
- 优化不同DPI设置的兼容性
- 支持更多纸张尺寸

---

**现在CPCL打印功能已经过全面优化，中文显示和字体大小问题应该得到解决！** 🎉
