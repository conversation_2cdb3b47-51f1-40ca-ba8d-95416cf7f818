# 打印机指令调试指南

## 🔍 问题分析

### 现象：打印机抖了一下但没有输出
- ✅ **连接成功**: 打印机抖动说明BLE连接正常，接收到了指令
- ❌ **指令问题**: 可能是指令格式不正确或打印机不支持该指令集
- ❌ **编码问题**: 中文字符编码可能不正确
- ❌ **纸张问题**: 可能没有纸张或纸张设置不正确

## 🛠️ 调试步骤

### 1. 基础连接测试
首先确认BLE连接和数据传输是否正常：

**测试方法**：
1. 连接打印机后点击"测试打印"
2. 选择"基础连接测试"
3. 发送最简单的指令：初始化 + "Test" + 换行

**预期结果**：
- 如果打印机支持ESC/POS，应该打印出"Test"
- 如果没有输出，说明指令集不匹配

### 2. 指令集测试
测试不同的指令集：

**ESC/POS测试**：
1. 选择"ESC/POS指令测试"
2. 发送标准ESC/POS指令
3. 观察打印机反应

**CPCL测试**：
1. 选择"CPCL指令测试"  
2. 发送CPCL标签指令
3. 观察打印机反应

### 3. 查看调试日志
在微信开发者工具控制台查看详细日志：

```javascript
// 连接信息
尝试连接设备: {deviceId: "xxx", name: "xxx"}
BLE连接创建成功
设备服务列表: [{uuid: "xxx"}]
找到打印服务: xxx 特征: yyy

// 数据发送信息
开始发送数据，总长度: 50 字节
数据内容: 0x1b 0x40 0x54 0x65 0x73 0x74 0x0a
发送第1块数据: 0x1b 0x40 0x54 0x65 0x73 0x74 0x0a
已发送 20/50 字节
数据发送完成，总计: 50 字节
```

## 📋 常见问题解决

### 1. 指令集不匹配
**问题**: 发送ESC/POS指令但打印机只支持CPCL
**解决**: 
- 在打印设置中切换指令集为"CPCL"
- 重新进行测试打印

### 2. 字符编码问题
**问题**: 英文能打印，中文乱码或不显示
**解决**:
- ESC/POS指令已使用GBK/UTF-8编码
- 某些打印机可能需要特定的字符集设置
- 可以先测试纯英文内容

### 3. 纸张设置问题
**问题**: 指令发送成功但没有纸张输出
**解决**:
- 检查打印机是否有纸张
- 确认纸张尺寸设置正确
- 某些打印机需要特定的纸张检测指令

### 4. 打印机模式问题
**问题**: 打印机处于错误的工作模式
**解决**:
- 重启打印机
- 检查打印机是否处于标签模式
- 某些打印机有连续纸/标签纸模式切换

## 🔧 指令集详解

### ESC/POS指令集
适用于大多数热敏打印机：

```javascript
// 基础指令
0x1B, 0x40        // 初始化打印机
0x1B, 0x61, 0x01  // 居中对齐
0x1B, 0x45, 0x01  // 加粗开启
0x1B, 0x45, 0x00  // 加粗关闭
0x0A              // 换行
0x1B, 0x64, 0x03  // 走纸3行
```

### CPCL指令集
适用于标签打印机：

```javascript
// 标签开始
"! 0 200 200 480 1\r\n"  // 标签设置
"TEXT 4 0 10 10 Hello\r\n"  // 文本输出
"PRINT\r\n"              // 打印标签
```

## 📱 实际操作步骤

### 步骤1：确认连接
1. 开启调试模式搜索设备
2. 连接目标打印机
3. 确认连接状态显示"已连接"

### 步骤2：基础测试
1. 点击"测试打印"
2. 选择"基础连接测试"
3. 观察是否有"Test"输出

### 步骤3：指令集测试
如果基础测试失败：
1. 尝试"ESC/POS指令测试"
2. 如果仍失败，尝试"CPCL指令测试"
3. 观察哪种指令集有响应

### 步骤4：设置优化
找到有效指令集后：
1. 在打印设置中选择对应指令集
2. 调整纸张尺寸匹配实际标签纸
3. 调整打印密度和速度

### 步骤5：最终验证
1. 进行完整的测试打印
2. 确认所有信息都能正确输出
3. 保存配置供后续使用

## ⚠️ 注意事项

### 硬件检查
- 确保打印机有足够电量
- 检查纸张是否正确安装
- 确认打印机处于正常工作状态

### 软件设置
- 某些打印机需要特定的波特率设置
- 部分打印机有多种工作模式
- 指令发送间隔可能需要调整

### 兼容性
- 不同品牌打印机可能有细微差异
- 某些指令可能需要特定参数
- 建议查阅打印机具体的指令手册

## 📞 进一步调试

如果以上步骤都无法解决问题：

1. **查看打印机手册**: 确认支持的指令集和参数
2. **联系厂商**: 获取具体的BLE通信协议
3. **使用专用软件**: 先用厂商提供的软件测试
4. **抓包分析**: 分析厂商软件发送的具体指令

---

**通过系统的调试步骤，应该能够找到适合您打印机的正确指令格式！** 🎯
