# CPCL蓝牙打印机配置指南

## 🎯 功能概述

LIMS小程序现已优化为专门支持CPCL指令集的蓝牙标签打印机，提供更简单、更可靠的打印机配置体验。

## 📱 快速配置流程

### 1. 进入配置页面
- 首页 → 快捷功能 → 打印机配置

### 2. 准备打印机
- 开启打印机电源
- 长按电源键3-5秒进入配对模式（指示灯闪烁）

### 3. 搜索并连接
1. 点击"搜索设备"按钮
2. 等待10秒完成搜索
3. 在设备列表中选择您的打印机
4. 点击连接（无名称设备会弹出确认对话框）

### 4. 测试打印
- 连接成功后点击"测试打印"
- 确认打印机输出测试标签

### 5. 调整设置
- 根据标签纸规格调整纸张尺寸
- 设置打印密度和速度

## 🖨️ 支持的打印机品牌

### CPCL兼容打印机
- **TSC**: TTP系列标签打印机
- **Zebra**: ZD系列标签打印机  
- **佳博**: 支持CPCL的GP系列
- **汉印**: 支持CPCL的HM系列

### 技术要求
- 蓝牙4.0+ (BLE)
- CPCL指令集支持
- 热敏打印技术
- 标签纸：58-80mm宽度

## 🔍 设备识别技巧

### 信号强度参考
- **-40 ~ -60dBm**: 优先尝试（设备很近）
- **-60 ~ -80dBm**: 可以尝试（设备较近）
- **< -80dBm**: 不建议（设备太远）

### 设备特征
- 有"有广播数据"标签的设备更活跃
- 无名称设备也可能是BLE打印机
- 优先尝试信号强度好的设备

## ⚠️ 常见问题解决

### 1. 搜索不到设备
**解决方案**：
- 确保打印机处于配对模式
- 搜索时靠近打印机（1-2米内）
- 多次搜索，BLE设备发现需要时间
- 重启打印机重新配对

### 2. 连接失败
**解决方案**：
- 检查打印机是否被其他设备连接
- 重启打印机清除连接状态
- 确保设备距离适中
- 检查打印机电量

### 3. 连接成功但不打印
**解决方案**：
- 检查是否有标签纸
- 确认标签纸安装正确
- 调整打印密度为"浓"
- 确认打印机支持CPCL指令

### 4. 打印内容不完整
**解决方案**：
- 调整纸张尺寸匹配实际标签纸
- 检查标签纸是否卡纸
- 调整打印速度为"慢"
- 确保打印机电量充足

## 📋 推荐设置

### 标准75mm×60mm标签纸
- 纸张宽度：75mm
- 纸张高度：60mm
- 打印密度：中
- 打印速度：正常
- 指令集：CPCL（固定）

### 其他常用规格
- **58mm×40mm**: 小型标签
- **80mm×60mm**: 大型标签
- **75mm×50mm**: 中型标签

## 💡 使用技巧

### 提高连接成功率
- 搜索前确保打印机配对模式
- 保持1-2米距离
- 优先选择信号强的设备
- 关注"有广播数据"标签

### 优化打印效果
- 使用推荐的75mm×60mm标签纸
- 打印密度设为"中"或"浓"
- 保持标签纸质量良好
- 定期清洁打印头

### 故障预防
- 记录成功连接的设备ID
- 定期测试打印功能
- 保持打印机电量充足
- 及时更换标签纸

## 🔄 重新配置

### 更换打印机
1. 点击"断开连接"
2. 搜索新设备
3. 连接新打印机
4. 测试打印验证

### 清除所有设置
1. 点击"清除设置"
2. 确认清除操作
3. 重新配置打印机

## 📞 获取帮助

### 配置页面帮助
- 点击"使用说明"查看详细指导
- 查看设备列表中的设备信息
- 观察连接状态和错误提示

### 调试信息
- 打开微信开发者工具查看控制台日志
- 记录设备ID和连接错误信息
- 截图保存设备列表用于分析

---

## 🎉 配置完成

配置成功后：
1. 返回样品管理页面
2. 点击瓶组的"打印"按钮
3. 系统自动使用CPCL指令打印标签
4. 享受便捷的无线打印体验

**现在系统专为CPCL打印机优化，配置更简单，打印更可靠！** 🚀
