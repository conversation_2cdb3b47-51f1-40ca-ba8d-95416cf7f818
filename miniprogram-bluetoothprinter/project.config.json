{"description": "项目配置文件", "packOptions": {"ignore": []}, "setting": {"urlCheck": true, "es6": true, "postcss": true, "minified": true, "newFeature": true, "nodeModules": true}, "compileType": "miniprogram", "libVersion": "2.4.0", "appid": "wx9813ba0436bd5a97", "projectname": "bluetoothprinter", "debugOptions": {"hidedInDevtools": []}, "isGameTourist": false, "condition": {"search": {"current": -1, "list": []}, "conversation": {"current": -1, "list": []}, "game": {"currentL": -1, "list": []}, "miniprogram": {"current": -1, "list": []}}}