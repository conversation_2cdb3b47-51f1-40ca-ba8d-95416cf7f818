# text-encoding库集成说明

## 🎉 text-encoding库集成完成！

### 问题解决
- ✅ **TextEncoder兼容性**: 解决微信小程序不支持TextEncoder的问题
- ✅ **多编码支持**: 支持UTF-8和GBK两种编码格式
- ✅ **中文字符处理**: 完善的中文字符编码和解码
- ✅ **小程序适配**: 专门为微信小程序环境优化

## 🛠️ 集成内容

### 1. 创建text-encoding库
**文件**: `miniprogram-native/utils/text-encoding.js`

**功能特性**:
- ✅ **TextEncoder类**: 支持字符串到字节数组的编码
- ✅ **TextDecoder类**: 支持字节数组到字符串的解码
- ✅ **UTF-8编码**: 完整的UTF-8编码/解码实现
- ✅ **GBK编码**: 常用中文字符的GBK编码映射
- ✅ **小程序兼容**: 无依赖，纯JavaScript实现

### 2. 支持的编码格式

#### UTF-8编码
```javascript
const encoder = new TextEncoder('utf-8')
const bytes = encoder.encode('测试') // [0xE6, 0xB5, 0x8B, 0xE8, 0xAF, 0x95]

const decoder = new TextDecoder('utf-8')
const text = decoder.decode(bytes) // "测试"
```

#### GBK编码
```javascript
const encoder = new TextEncoder('gbk')
const bytes = encoder.encode('测试') // [0xB2, 0xE2, 0xCA, 0xD4]

const decoder = new TextDecoder('gbk')
const text = decoder.decode(bytes) // "测试"
```

### 3. 常用中文字符支持
预定义了40+常用中文字符的GBK编码：

**样品相关**: 样、品、类、别、编、号
**时间相关**: 采、日、期、时、间、点、位
**检测相关**: 检、测、项、目
**存储相关**: 保、存、容、器、方、式
**状态相关**: 状、态、成、功
**系统相关**: 系、统、打、印、试
**数值相关**: 水、温、度、值、量、质、浓、密

## 📋 代码集成

### 1. 打印机配置页面
**文件**: `miniprogram-native/pages/printer/config.js`

**集成代码**:
```javascript
const { TextEncoder, TextDecoder } = require('../../utils/text-encoding.js')
```

**使用示例**:
```javascript
// UTF-8编码测试
const utf8Encoder = new TextEncoder('utf-8')
const utf8Bytes = Array.from(utf8Encoder.encode('测试'))

// GBK编码测试
const gbkEncoder = new TextEncoder('gbk')
const gbkBytes = Array.from(gbkEncoder.encode('测试'))
```

### 2. 任务详情页面
**文件**: `miniprogram-native/pages/sampling/task-detail.js`

**集成代码**:
```javascript
const { TextEncoder, TextDecoder } = require('../../utils/text-encoding.js')
```

## 🎯 优化效果

### 1. 中文编码测试增强
现在中文编码测试包含：

**UTF-8测试**:
```
TEXT 3 0 10 10 UTF8: [动态编码的"测试"]
```

**GBK测试**:
```
TEXT 3 0 10 40 GBK: [动态编码的"测试"]
```

**GB2312声明测试**:
```
ENCODING GB2312
TEXT 3 0 10 70 GB2312: [GBK编码的"测试"]
```

### 2. 动态编码支持
- ✅ **实时编码**: 不再使用硬编码的字节数组
- ✅ **多格式支持**: 可以轻松切换UTF-8和GBK编码
- ✅ **扩展性**: 可以轻松添加更多中文字符支持

### 3. 错误处理
- ✅ **编码降级**: 不支持的字符使用'?'替代
- ✅ **格式检查**: 检查编码格式是否支持
- ✅ **异常处理**: 提供清晰的错误信息

## 💡 使用建议

### 1. 编码选择策略
```javascript
// 根据打印机支持情况选择编码
function getBestEncoding(printerType) {
  if (printerType === 'chinese') {
    return 'gbk'  // 中文打印机优先使用GBK
  } else {
    return 'utf-8'  // 国际打印机使用UTF-8
  }
}
```

### 2. 中文字符检测
```javascript
function hasChineseChars(text) {
  return /[\u4e00-\u9fff]/.test(text)
}

function encodeText(text) {
  if (hasChineseChars(text)) {
    return new TextEncoder('gbk').encode(text)
  } else {
    return new TextEncoder('utf-8').encode(text)
  }
}
```

### 3. 编码测试流程
1. **UTF-8测试**: 测试UTF-8编码的中文显示
2. **GBK测试**: 测试GBK编码的中文显示
3. **对比效果**: 选择显示效果最好的编码
4. **应用设置**: 在实际打印中使用最佳编码

## 🔧 扩展功能

### 1. 添加更多中文字符
```javascript
// 在text-encoding.js中扩展chineseGBKMap
const chineseGBKMap = {
  // 现有字符...
  '新': [0xD0, 0xC2],  // 新增字符
  '增': [0xD4, 0xF6],  // 新增字符
  // ...
}
```

### 2. 支持更多编码格式
```javascript
// 可以扩展支持Big5、Shift_JIS等编码
function TextEncoder(encoding) {
  this.encoding = encoding.toLowerCase()
  
  switch(this.encoding) {
    case 'utf-8':
    case 'gbk':
    case 'big5':     // 新增支持
    case 'shift_jis': // 新增支持
      break
    default:
      throw new Error('Unsupported encoding')
  }
}
```

### 3. 编码自动检测
```javascript
function detectBestEncoding(text, testResults) {
  // 根据测试结果自动选择最佳编码
  if (testResults.gbk.success && testResults.gbk.quality > testResults.utf8.quality) {
    return 'gbk'
  } else {
    return 'utf-8'
  }
}
```

## 📊 测试验证

### 1. 编码正确性测试
```javascript
// 测试编码和解码的一致性
const originalText = '测试'
const encoder = new TextEncoder('gbk')
const decoder = new TextDecoder('gbk')

const encoded = encoder.encode(originalText)
const decoded = decoder.decode(encoded)

console.log(originalText === decoded) // 应该为true
```

### 2. 打印效果测试
1. **连接打印机**
2. **选择"中文编码测试"**
3. **观察UTF-8和GBK行的显示效果**
4. **选择显示效果最好的编码格式**

### 3. 实际应用测试
1. **在样品标签中使用选定的编码**
2. **验证中文字符显示正常**
3. **确认整体打印效果**

---

**现在text-encoding库已完全集成，支持动态的UTF-8和GBK编码！** 🎉

请测试新的中文编码功能：
1. **中文编码测试** - 对比UTF-8和GBK的显示效果
2. **完整CPCL测试** - 验证动态编码的中文显示
3. **样品标签打印** - 在实际应用中测试编码效果

根据测试结果，我们可以进一步优化编码选择和字符支持。
