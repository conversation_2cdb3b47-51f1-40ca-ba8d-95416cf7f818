# LIMS系统局域网配置指南

## 📋 当前网络信息
- **服务器IP**: ************
- **后端端口**: 9099
- **前端端口**: 4000

## 🔧 1. 后端配置

### 1.1 确认后端配置
后端已正确配置为监听所有网络接口：
```python
# back/config/env.py
app_host: str = "0.0.0.0"  # ✅ 已配置
app_port: int = 9099       # ✅ 端口9099
```

### 1.2 启动后端服务
```bash
cd back
python app.py
```

### 1.3 验证后端服务
```bash
# 检查服务是否在监听
ss -tlnp | grep 9099

# 应该看到类似输出：
# LISTEN 0 128 0.0.0.0:9099 0.0.0.0:*
```

## 🌐 2. 前端配置

### 2.1 修改前端API地址
编辑前端配置文件，将API地址改为服务器IP：

**方法一：修改环境配置文件**
```bash
# front/.env.development
VUE_APP_BASE_API = 'http://************:9099/dev-api'

# front/.env.production  
VUE_APP_BASE_API = 'http://************:9099/dev-api'
```

**方法二：修改配置文件**
```javascript
// front/src/utils/request.js 或相关配置文件
const baseURL = 'http://************:9099/dev-api'
```

### 2.2 启动前端服务
```bash
cd front
npm run dev -- --host 0.0.0.0
```

## 📱 3. 小程序配置

### 3.1 修改小程序API地址
```javascript
// miniprogram-native/app.js
const config = {
  baseUrl: 'http://************:9099'  // 修改为服务器IP
}
```

### 3.2 配置微信开发者工具
1. 打开微信开发者工具
2. 进入项目设置
3. 勾选"不校验合法域名、web-view（业务域名）、TLS 版本以及 HTTPS 证书"
4. 重新编译项目

## 🔥 4. 防火墙配置

### 4.1 Ubuntu/Debian系统
```bash
# 检查防火墙状态
sudo ufw status

# 如果防火墙开启，允许端口
sudo ufw allow 9099
sudo ufw allow 4000

# 重新加载防火墙
sudo ufw reload
```

### 4.2 CentOS/RHEL系统
```bash
# 检查防火墙状态
sudo firewall-cmd --state

# 允许端口
sudo firewall-cmd --permanent --add-port=9099/tcp
sudo firewall-cmd --permanent --add-port=4000/tcp

# 重新加载防火墙
sudo firewall-cmd --reload
```

## 🔍 5. 网络测试

### 5.1 测试后端连接
在局域网其他设备上测试：
```bash
# 测试端口连通性
telnet ************ 9099

# 或使用curl测试API
curl http://************:9099/dev-api/captchaImage
```

### 5.2 浏览器测试
在局域网其他设备的浏览器中访问：
- 后端API: http://************:9099/dev-api/docs
- 前端页面: http://************:4000

## 📋 6. 常见问题解决

### 6.1 连接被拒绝
- 检查服务是否正常启动
- 检查防火墙设置
- 确认IP地址正确

### 6.2 CORS跨域问题
后端已配置CORS中间件，如果仍有问题，检查：
```python
# back/middlewares/cors_middleware.py
# 确保允许所有来源或添加具体IP
```

### 6.3 小程序网络请求失败
- 确保勾选了"不校验合法域名"
- 检查request.js中的baseUrl配置
- 查看控制台网络请求日志

## 🚀 7. 启动脚本

### 7.1 后端启动脚本
```bash
#!/bin/bash
# start_backend.sh
cd /home/<USER>/workspaces/lims2/back
source venv/bin/activate
python app.py
```

### 7.2 前端启动脚本
```bash
#!/bin/bash
# start_frontend.sh
cd /home/<USER>/workspaces/lims2/front
npm run dev -- --host 0.0.0.0 --port 4000
```

## 📱 8. 移动设备访问

### 8.1 手机浏览器
直接访问：http://************:4000

### 8.2 微信小程序
1. 在微信开发者工具中预览
2. 扫码在手机微信中打开
3. 确保手机和服务器在同一局域网

## 🔒 9. 安全建议

### 9.1 生产环境
- 配置HTTPS证书
- 设置具体的CORS允许域名
- 配置防火墙只允许必要端口
- 使用反向代理（如Nginx）

### 9.2 开发环境
- 定期更新依赖包
- 不要在生产环境使用开发配置
- 定期备份数据库

## 📞 10. 技术支持

如果遇到问题，请检查：
1. 服务器日志：`back/logs/`
2. 浏览器控制台错误
3. 网络连接状态
4. 防火墙配置

---

**配置完成后，局域网内的所有设备都可以通过 http://************:4000 访问LIMS系统！**
