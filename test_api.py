#!/usr/bin/env python3
"""
测试LIMS后端API接口
"""
import requests
import json

# 配置
BASE_URL = "http://127.0.0.1:9099"
TEST_TOKEN = "test_token"  # 测试token，可以跳过认证

def test_api_endpoint(endpoint, method="GET", data=None, headers=None):
    """测试API端点"""
    url = f"{BASE_URL}{endpoint}"
    
    # 默认headers
    default_headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {TEST_TOKEN}"
    }
    
    if headers:
        default_headers.update(headers)
    
    try:
        if method.upper() == "GET":
            response = requests.get(url, headers=default_headers, timeout=10)
        elif method.upper() == "POST":
            response = requests.post(url, json=data, headers=default_headers, timeout=10)
        else:
            print(f"不支持的HTTP方法: {method}")
            return None
            
        print(f"\n=== 测试 {method} {endpoint} ===")
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        try:
            response_data = response.json()
            print(f"响应数据: {json.dumps(response_data, indent=2, ensure_ascii=False)}")
            return response_data
        except json.JSONDecodeError:
            print(f"响应内容 (非JSON): {response.text}")
            return response.text
            
    except requests.exceptions.RequestException as e:
        print(f"\n=== 测试 {method} {endpoint} 失败 ===")
        print(f"错误: {e}")
        return None

def main():
    """主测试函数"""
    print("开始测试LIMS后端API...")
    
    # 测试基本连接
    print("\n1. 测试基本连接")
    test_api_endpoint("/docs")
    
    # 测试我的任务接口
    print("\n2. 测试我的任务接口")
    test_api_endpoint("/sampling/task/my-tasks")
    
    # 测试任务分页接口
    print("\n3. 测试任务分页接口")
    test_api_endpoint("/sampling/task/page?page=1&size=10")
    
    # 测试统计接口
    print("\n4. 测试统计接口")
    test_api_endpoint("/sampling/task/stats")
    
    print("\n测试完成!")

if __name__ == "__main__":
    main()
