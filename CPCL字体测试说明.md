# CPCL字体测试说明

## 🔍 当前字体问题

### 问题现象
- ❌ **中文显示为竖条**: 字体不支持中文字符
- ❌ **字体太大**: 字体尺寸设置不当

### 当前使用的字体
- **字体ID**: 1
- **字体方向**: 0 (正常方向)
- **字体放大**: 无 (SETMAG 1 1)

## 🛠️ CPCL字体系统

### 字体ID说明
CPCL支持多种内置字体，不同字体有不同特性：

| 字体ID | 字体名称 | 特点 | 中文支持 |
|--------|----------|------|----------|
| 0 | 最小字体 | 6x8像素 | 可能不支持 |
| 1 | 小字体 | 8x12像素 | 部分支持 |
| 2 | 中字体 | 10x16像素 | 较好支持 |
| 3 | 大字体 | 12x20像素 | 较好支持 |
| 4 | 特大字体 | 14x24像素 | 最好支持 |
| 5 | 超大字体 | 32x48像素 | 支持但很大 |
| 6 | 自定义字体1 | 取决于打印机 | 未知 |
| 7 | 自定义字体2 | 取决于打印机 | 未知 |

### 字体方向
| 方向值 | 旋转角度 | 说明 |
|--------|----------|------|
| 0 | 0° | 正常方向 |
| 1 | 90° | 顺时针90度 |
| 2 | 180° | 倒置 |
| 3 | 270° | 逆时针90度 |

## 🎯 字体测试功能

### 新增测试选项
现在测试打印包含4个选项：
1. **基础CPCL测试** - 基础功能测试
2. **完整CPCL测试** - 完整内容测试
3. **中文编码测试** - 编码方式测试
4. **字体测试** - 字体和方向测试

### 字体测试内容
**测试指令**：
```
! 0 200 200 400 1

# 字体ID测试 (0-7)
TEXT 0 0 10 10 Font0: Font Test [测试]
TEXT 1 0 10 45 Font1: Font Test [测试]
TEXT 2 0 10 80 Font2: Font Test [测试]
TEXT 3 0 10 115 Font3: Font Test [测试]
TEXT 4 0 10 150 Font4: Font Test [测试]
TEXT 5 0 10 185 Font5: Font Test [测试]
TEXT 6 0 10 220 Font6: Font Test [测试]
TEXT 7 0 10 255 Font7: Font Test [测试]

# 字体方向测试 (0-3)
TEXT 1 0 10 300 R0: [测试]
TEXT 1 1 10 325 R1: [测试]
TEXT 1 2 10 350 R2: [测试]
TEXT 1 3 10 375 R3: [测试]

PRINT
```

## 📋 测试步骤

### 第1步：字体测试
1. 连接打印机
2. 点击"测试打印"
3. 选择"字体测试"
4. 观察打印结果

### 第2步：分析结果

**观察要点**：
- **哪个字体ID的中文显示正常？**
- **哪个字体大小合适？**
- **是否有字体支持中文但不会显示为竖条？**

**可能的结果**：

#### 情况1：某个字体ID中文正常
```
Font0: Font Test [乱码]
Font1: Font Test [竖条]
Font2: Font Test [测试] ← 正常显示
Font3: Font Test [测试] ← 正常显示但较大
```
**解决方案**: 使用显示正常且大小合适的字体ID

#### 情况2：所有字体都显示竖条
```
Font0: Font Test [||||]
Font1: Font Test [||||]
Font2: Font Test [||||]
...
```
**解决方案**: 打印机字体不支持中文，需要其他方案

#### 情况3：高字体ID支持中文
```
Font0-3: Font Test [乱码/竖条]
Font4-7: Font Test [测试] ← 高ID字体支持
```
**解决方案**: 使用支持中文的高ID字体

## 🔧 根据测试结果优化

### 1. 找到最佳字体ID
如果测试发现字体ID 3支持中文且大小合适：

```javascript
// 修改所有TEXT指令
TEXT 3 0 10 10 content  // 使用字体ID 3
```

### 2. 调整字体大小
如果字体太大，可以：

**方案1**: 使用更小的字体ID
```javascript
TEXT 0 0 10 10 content  // 最小字体
```

**方案2**: 调整DPI
```javascript
! 0 150 150 height 1    // 降低DPI
```

**方案3**: 使用字体放大控制
```javascript
SETMAG 1 1              // 不放大
// 或
SETMAG 0 0              // 最小放大（如果支持）
```

### 3. 中文字体方案

**如果找到支持中文的字体**：
```javascript
// 使用支持中文的字体ID
generateCPCLCommands() {
  const CHINESE_FONT_ID = 3  // 根据测试结果确定
  commands.push(...this.stringToBytes(`TEXT ${CHINESE_FONT_ID} 0 x y `))
  commands.push(...chineseBytes)
}
```

**如果所有字体都不支持中文**：
```javascript
// 方案1: 纯英文标签
'样品类别' → 'Sample Type'
'样品编号' → 'Sample No'

// 方案2: 拼音标签  
'样品类别' → 'YangPin LeiBie'

// 方案3: 图片方式（复杂）
// 将中文转换为图片再打印
```

## 💡 优化建议

### 1. 字体选择策略
```javascript
// 根据内容选择字体
if (content.includes('中文')) {
  fontId = BEST_CHINESE_FONT_ID  // 测试确定的最佳中文字体
} else {
  fontId = 1  // 英文使用小字体
}
```

### 2. 动态字体调整
```javascript
// 根据字符长度调整字体
if (text.length > 20) {
  fontId = 0  // 长文本使用小字体
} else {
  fontId = 2  // 短文本使用中字体
}
```

### 3. 字体兼容性处理
```javascript
// 字体降级策略
const fontPriority = [3, 2, 4, 1, 0]  // 优先级顺序
for (let fontId of fontPriority) {
  if (testFontSupport(fontId)) {
    return fontId
  }
}
```

## 🎯 测试目标

通过字体测试，我们要确定：

1. **最佳中文字体ID** - 支持中文且显示正常
2. **合适的字体大小** - 不会太大或太小
3. **字体兼容性** - 在您的打印机上的实际效果
4. **备选方案** - 如果中文不支持的替代方案

---

**请进行字体测试，然后告诉我哪个字体ID的中文显示效果最好！** 🎯

这样我们就能找到适合您打印机的最佳字体设置。
