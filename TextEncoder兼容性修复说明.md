# TextEncoder兼容性修复说明

## 🔍 问题诊断

### 错误信息
```
测试打印失败: {message: "Can't find variable: TextEncoder", ...}
```

### 根本原因
- ❌ **API不支持**: 微信小程序不支持 `TextEncoder` API
- ❌ **环境限制**: 小程序运行环境与浏览器环境不同
- ❌ **编码依赖**: 代码依赖了浏览器特有的编码API

## 🛠️ 修复方案

### 1. 移除TextEncoder依赖
**修复前**：
```javascript
const encoder = new TextEncoder()
const chineseBytes = encoder.encode(chineseText)
```

**修复后**：
```javascript
const chineseBytes = [0xE6, 0xB5, 0x8B, 0xE8, 0xAF, 0x95] // "测试"的UTF-8编码
```

### 2. 手动UTF-8编码
添加了常用中文字符的UTF-8编码映射：

```javascript
chineseToUTF8(char) {
  const commonChars = {
    '测': [0xE6, 0xB5, 0x8B],
    '试': [0xE8, 0xAF, 0x95],
    '打': [0xE6, 0x89, 0x93],
    '印': [0xE5, 0x8D, 0xB0],
    '样': [0xE6, 0xA0, 0xB7],
    '品': [0xE5, 0x93, 0x81],
    // ... 更多字符
  }
  return commonChars[char] || []
}
```

### 3. 优化测试指令
现在中文编码测试包含：

1. **UTF-8编码测试**：
   ```
   TEXT 1 0 10 10 UTF8: [0xE6, 0xB5, 0x8B, 0xE8, 0xAF, 0x95]
   ```

2. **GBK编码测试**：
   ```
   TEXT 1 0 10 70 GBK: [0xB2, 0xE2, 0xCA, 0xD4]
   ```

3. **十六进制显示**：
   ```
   TEXT 1 0 10 130 UTF8-HEX: E6 B5 8B E8 AF 95
   TEXT 1 0 10 160 GBK-HEX: B2 E2 CA D4
   ```

## ✅ 修复结果

### 1. 兼容性问题解决
- ✅ **移除TextEncoder**: 不再依赖浏览器API
- ✅ **手动编码**: 使用预定义的字节数组
- ✅ **小程序兼容**: 完全兼容微信小程序环境

### 2. 功能增强
- ✅ **多编码测试**: 同时测试UTF-8和GBK编码
- ✅ **十六进制显示**: 显示实际的字节码便于调试
- ✅ **常用字符支持**: 预定义了30+常用中文字符

### 3. 调试信息优化
- ✅ **编码对比**: 可以对比UTF-8和GBK的显示效果
- ✅ **字节码显示**: 直观显示编码的十六进制值
- ✅ **ASCII对照**: 纯英文对照确认基础功能

## 🎯 测试建议

### 现在可以正常测试
1. **基础CPCL测试**: 验证打印机基础功能
2. **完整CPCL测试**: 测试英文内容和布局
3. **中文编码测试**: 对比不同编码方式的效果

### 中文编码测试结果分析

**如果UTF-8行显示正常**：
- 打印机支持UTF-8编码
- 可以使用UTF-8进行中文打印

**如果GBK行显示正常**：
- 打印机需要GBK编码
- 应该使用GBK编码方式

**如果都显示异常**：
- 打印机可能不支持中文
- 考虑使用纯英文标签

**如果显示为竖条**：
- 字体不支持中文字符
- 需要更换字体或使用图片方式

## 📋 常用中文字符编码表

### UTF-8编码
```
测: E6 B5 8B    试: E8 AF 95    打: E6 89 93    印: E5 8D B0
样: E6 A0 B7    品: E5 93 81    类: E7 B1 BB    别: E5 88 AB
编: E7 BC 96    号: E5 8F B7    采: E9 87 87    日: E6 97 A5
期: E6 9C 9F    时: E6 97 B6    间: E9 97 B4    点: E7 82 B9
```

### GBK编码
```
测: B2 E2       试: CA D4       打: B4 F2       印: D3 A1
样: D1 F9       品: C6 B7       类: C0 E0       别: B1 F0
编: B1 E0       号: BA C5       采: B2 C9       日: C8 D5
期: C6 DA       时: CA B1       间: BC E4       点: B5 E3
```

## 🔄 后续优化

### 1. 扩展字符支持
可以继续添加更多中文字符的编码映射：
```javascript
// 添加更多字符
'水': [0xE6, 0xB0, 0xB4],  // UTF-8
'温': [0xE6, 0xB8, 0xA9],  // UTF-8
// 或
'水': [0xCB, 0xAE],        // GBK
'温': [0xCE, 0xC2],        // GBK
```

### 2. 动态编码转换
实现完整的Unicode到UTF-8/GBK转换：
```javascript
stringToUTF8(str) {
  const bytes = []
  for (let i = 0; i < str.length; i++) {
    const code = str.charCodeAt(i)
    // Unicode到UTF-8的完整转换逻辑
  }
  return bytes
}
```

### 3. 编码自动检测
根据测试结果自动选择最佳编码：
```javascript
// 测试不同编码，选择显示效果最好的
const bestEncoding = detectBestEncoding(testResults)
```

---

**现在TextEncoder兼容性问题已完全解决，可以正常进行打印测试了！** 🎉

请重新测试打印功能，应该不会再出现TextEncoder错误。
