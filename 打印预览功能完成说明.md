# 打印预览功能完成说明

## 🎉 打印预览功能完成！

### 功能概述
参考H5版本的实现，为小程序添加了完整的标签打印预览功能，用户可以在打印前预览标签内容，确认无误后再进行打印。

## 🛠️ 实现内容

### 1. 新增预览页面
**文件**: `miniprogram-native/pages/printer/preview.*`

**功能特性**:
- ✅ **Canvas预览**: 使用Canvas绘制75mm×60mm标签预览
- ✅ **标签内容详情**: 显示完整的标签信息
- ✅ **打印机状态**: 显示当前连接的打印机
- ✅ **确认打印**: 预览确认后执行打印

### 2. 标签内容格式
**参考H5版本实现**，标签包含以下信息：

#### 主要字段
- ✅ **样品类别**: 从任务信息获取检测类别
- ✅ **样品编号**: 格式为"任务编号（序号/总数）"
- ✅ **采样日期**: 格式化的采样日期
- ✅ **采样点位**: 格式为"点位名称 （周期N）"
- ✅ **检测项目**: 检测项目列表
- ✅ **保存容器**: 容器类型和规格
- ✅ **保存方式**: 保存条件和要求
- ✅ **检测单位**: 浙江求实环境监测有限公司
- ✅ **页码**: 1/1

#### 数据来源
```javascript
// 样品编号构建
const taskCode = taskInfo.taskCode || taskInfo.projectName || 'LIMS001'
const sampleIndex = bottleGroup.sortOrder || 1
const totalSamples = taskInfo.totalSamples || bottleGroup.totalCount || 1
const sampleNumber = `${taskCode}（${sampleIndex}/${totalSamples}）`

// 采样点位构建
const pointName = taskInfo.pointName || taskInfo.location || '采样点位'
const cycleNumber = taskInfo.cycleNumber || bottleGroup.cycleNumber || 1
const samplingPoint = `${pointName} （周期${cycleNumber}）`
```

### 3. 预览界面设计

#### 顶部导航
- ✅ **返回按钮**: 返回任务详情页面
- ✅ **页面标题**: "标签预览"
- ✅ **自定义导航**: 统一的界面风格

#### 预览区域
- ✅ **Canvas绘制**: 300×240像素的标签预览
- ✅ **比例显示**: 75mm×60mm标签纸提示
- ✅ **边框样式**: 蓝色边框突出显示
- ✅ **内容布局**: 左侧文本信息，右侧二维码区域

#### 详情展示
- ✅ **字段列表**: 完整显示所有标签字段
- ✅ **标签值**: 实际的标签内容
- ✅ **清晰布局**: 标签名和值的对比显示

#### 底部操作
- ✅ **重新预览**: 刷新预览内容
- ✅ **确认打印**: 执行实际打印
- ✅ **打印机状态**: 显示连接状态

### 4. 打印流程优化

#### 原流程
```
点击打印 → 直接打印 → 完成
```

#### 新流程
```
点击打印 → 跳转预览 → 确认内容 → 执行打印 → 完成
```

#### 数据传递
```javascript
// 任务详情页面
const printData = {
  taskInfo: taskInfo,        // 任务信息
  bottleGroup: bottleGroup   // 瓶组信息（含序号）
}

wx.navigateTo({
  url: `/pages/printer/preview?data=${encodeURIComponent(JSON.stringify(printData))}`
})
```

### 5. Canvas预览实现

#### 绘制内容
```javascript
// 标签边框
ctx.strokeRect(5, 5, canvasWidth - 10, canvasHeight - 10)

// 文本内容（左侧）
ctx.fillText(`样品类别：${labelData.sampleCategory}`, leftMargin, yPos)
ctx.fillText(`样品编号：${labelData.sampleNumber}`, leftMargin, yPos + 18)
// ... 其他字段

// 二维码区域（右侧）
ctx.strokeRect(qrX, qrY, qrSize, qrSize)
ctx.fillText('二维码', qrX + qrSize/2, qrY + qrSize/2)

// 底部信息
ctx.fillText(labelData.companyName, leftMargin, canvasHeight - 15)
ctx.fillText(labelData.pageNumber, canvasWidth - 15, canvasHeight - 15)
```

### 6. 打印指令生成

#### CPCL指令优化
```javascript
// 标签开始
! 0 *********** 1

// 编码声明
ENCODING GB2312

// 文本输出（使用GBK编码）
TEXT 3 0 x y [GBK编码的中文内容]

// 二维码
BARCODE QR x y M 2 U 4
MA,[二维码内容]
ENDQR

// 打印执行
PRINT
```

## 📱 用户体验

### 1. 操作流程
1. **任务详情** → 点击瓶组"打印"按钮
2. **预览页面** → 查看标签预览和详细信息
3. **确认打印** → 点击"确认打印"按钮
4. **打印执行** → 自动连接打印机并打印
5. **完成返回** → 打印完成后自动返回

### 2. 界面特点
- ✅ **直观预览**: Canvas绘制的真实标签预览
- ✅ **详细信息**: 完整的标签字段展示
- ✅ **状态提示**: 清晰的打印机连接状态
- ✅ **操作简单**: 两步完成打印（预览→确认）

### 3. 错误处理
- ✅ **数据检查**: 验证标签数据完整性
- ✅ **打印机检查**: 检查打印机连接状态
- ✅ **错误提示**: 清晰的错误信息和解决建议
- ✅ **引导配置**: 未配置打印机时引导到配置页面

## 🔧 技术实现

### 1. 页面结构
```
preview/
├── preview.js      # 页面逻辑
├── preview.wxml    # 页面结构
├── preview.wxss    # 页面样式
└── preview.json    # 页面配置
```

### 2. 核心方法
- ✅ **generateLabelData()**: 生成标签数据
- ✅ **drawPreview()**: 绘制Canvas预览
- ✅ **generatePrintContent()**: 生成打印内容
- ✅ **generateCPCLCommands()**: 生成CPCL指令
- ✅ **sendBLEData()**: 发送蓝牙打印数据

### 3. 数据处理
- ✅ **URL参数传递**: 通过URL参数传递打印数据
- ✅ **JSON序列化**: 数据的序列化和反序列化
- ✅ **编码处理**: URL编码避免特殊字符问题

### 4. 样式设计
- ✅ **响应式布局**: 适配不同屏幕尺寸
- ✅ **现代化UI**: 圆角、阴影、渐变等现代元素
- ✅ **一致性**: 与整体应用风格保持一致

## 📊 与H5版本对比

### 相同功能
- ✅ **标签格式**: 完全相同的标签内容和布局
- ✅ **数据字段**: 相同的标签字段和数据来源
- ✅ **预览功能**: 都提供打印前预览
- ✅ **二维码**: 相同的二维码内容（样品编号）

### 小程序特色
- ✅ **Canvas预览**: 使用Canvas绘制预览（H5使用HTML）
- ✅ **蓝牙打印**: 直接蓝牙连接打印机（H5使用系统打印）
- ✅ **移动优化**: 专为移动端优化的界面
- ✅ **离线使用**: 支持离线打印功能

### 技术差异
- ✅ **渲染方式**: Canvas vs HTML
- ✅ **打印方式**: 蓝牙 vs 系统打印
- ✅ **数据传递**: URL参数 vs Props
- ✅ **样式实现**: WXSS vs CSS

## ✅ 测试验证

### 1. 功能测试
- [ ] 从任务详情点击打印按钮
- [ ] 预览页面正确显示标签内容
- [ ] Canvas预览绘制正确
- [ ] 标签详情信息完整
- [ ] 打印机状态显示正确

### 2. 数据测试
- [ ] 样品编号格式正确
- [ ] 采样点位格式正确
- [ ] 日期格式化正确
- [ ] 中文内容显示正常

### 3. 打印测试
- [ ] 确认打印功能正常
- [ ] CPCL指令生成正确
- [ ] 蓝牙数据传输成功
- [ ] 打印内容与预览一致

### 4. 界面测试
- [ ] 预览界面布局正确
- [ ] 响应式适配正常
- [ ] 操作按钮功能正常
- [ ] 返回导航正常

---

**现在LIMS小程序具备了完整的标签打印预览功能！** 🎉

**使用流程**:
1. **任务详情** → 点击瓶组"打印"按钮
2. **预览确认** → 查看标签内容和格式
3. **确认打印** → 执行蓝牙打印
4. **完成** → 获得与H5版本相同格式的标签

现在用户可以在打印前预览标签内容，确保打印结果符合预期！
