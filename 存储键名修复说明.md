# 存储键名修复说明

## 🔍 问题诊断

### 问题现象
用户配置了打印机后，在瓶组列表点击打印时提示"未配置打印机"。

### 根本原因
打印机配置页面和任务详情页面使用了不同的存储键名：

**修复前**：
- 配置页面保存：`wx.setStorageSync('connectedPrinter', deviceInfo)`
- 任务页面读取：`wx.getStorageSync('connectedDevice')`
- 结果：键名不匹配，读取不到配置信息

## 🛠️ 修复内容

### 1. 统一存储键名
将所有地方的存储键名统一为 `'connectedDevice'`：

**打印机配置页面 (config.js)**：
- ✅ 保存设备：`wx.setStorageSync('connectedDevice', deviceInfo)`
- ✅ 加载设备：`wx.getStorageSync('connectedDevice')`
- ✅ 删除设备：`wx.removeStorageSync('connectedDevice')`

**任务详情页面 (task-detail.js)**：
- ✅ 读取设备：`wx.getStorageSync('connectedDevice')` (已正确)

### 2. 修复的具体位置

#### config.js 文件修复：
1. **第30行** - 加载已保存设置：
   ```javascript
   // 修复前
   const savedDevice = wx.getStorageSync('connectedPrinter')
   // 修复后  
   const savedDevice = wx.getStorageSync('connectedDevice')
   ```

2. **第254行** - 保存连接设备：
   ```javascript
   // 修复前
   wx.setStorageSync('connectedPrinter', deviceInfo)
   // 修复后
   wx.setStorageSync('connectedDevice', deviceInfo)
   ```

3. **第267行** - 连接状态变化时删除：
   ```javascript
   // 修复前
   wx.removeStorageSync('connectedPrinter')
   // 修复后
   wx.removeStorageSync('connectedDevice')
   ```

4. **第308行** - 断开连接时删除：
   ```javascript
   // 修复前
   wx.removeStorageSync('connectedPrinter')
   // 修复后
   wx.removeStorageSync('connectedDevice')
   ```

5. **第651行** - 清除设置时删除：
   ```javascript
   // 修复前
   wx.removeStorageSync('connectedPrinter')
   // 修复后
   wx.removeStorageSync('connectedDevice')
   ```

## ✅ 验证结果

### 存储键名一致性检查
- ✅ **配置页面**: 全部使用 `'connectedDevice'`
- ✅ **任务页面**: 全部使用 `'connectedDevice'`
- ✅ **无冲突**: 不再存在 `'connectedPrinter'` 键名

### 功能流程验证
1. **配置打印机**: 
   - 搜索设备 → 连接设备 → 保存到 `'connectedDevice'`
   
2. **使用打印机**:
   - 读取 `'connectedDevice'` → 检查配置 → 执行打印

## 🎯 预期效果

修复后的完整流程：

1. **用户配置打印机**：
   ```javascript
   // 配置页面保存
   wx.setStorageSync('connectedDevice', {
     deviceId: 'xxx',
     name: 'xxx',
     serviceId: 'xxx',
     characteristicId: 'xxx'
   })
   ```

2. **用户点击打印**：
   ```javascript
   // 任务页面读取
   const connectedPrinter = wx.getStorageSync('connectedDevice')
   if (connectedPrinter) {
     // 找到配置，执行打印
     this.bluetoothPrint(printContent)
   } else {
     // 未找到配置，提示用户配置
     wx.showModal({ title: '未配置打印机' })
   }
   ```

## 🔄 测试建议

### 清除旧数据
如果之前已经配置过打印机，建议：
1. 在打印机配置页面点击"清除设置"
2. 重新搜索和连接打印机
3. 测试打印功能

### 验证步骤
1. **配置打印机**：首页 → 打印机配置 → 搜索连接
2. **测试配置**：配置页面 → 测试打印
3. **验证集成**：样品管理 → 点击瓶组打印按钮
4. **确认成功**：应该直接进入打印流程，不再提示未配置

---

**现在存储键名已完全统一，打印机配置和使用功能应该正常工作了！** 🎉
