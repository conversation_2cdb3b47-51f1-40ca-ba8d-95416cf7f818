from typing import List, Optional, Tuple
from sqlalchemy.ext.asyncio import AsyncSession

from module_sampling.dao.detection_cycle_item_dao import DetectionCycleItemDao
from module_sampling.entity.do.detection_cycle_item_do import DetectionCycleItem
from module_sampling.dto.detection_cycle_item_dto import DetectionCycleItemDTO
from module_quotation.service.project_quotation_service import ProjectQuotationService
from module_quotation.entity.do.project_quotation_item_do import ProjectQuotationItem
from sqlalchemy import select
from exceptions.exception import ServiceException
# from config.enums import ResponseCode  # ResponseCode 不存在，改用 message 参数
from typing import List


class DetectionCycleItemService:
    """检测周期条目服务"""
    
    def __init__(self, db):
        self.db = db
        self.detection_cycle_item_dao = DetectionCycleItemDao(db)
        self.project_quotation_service = ProjectQuotationService(db)
    
    async def generate_cycle_items_for_quotation(self, project_quotation_id: int, create_by: int, silent_mode: bool = False) -> List[DetectionCycleItemDTO]:
        """
        为项目报价生成检测周期条目

        :param project_quotation_id: 项目报价ID
        :param create_by: 创建人ID
        :param silent_mode: 静默模式，如果为True，则在已存在时不抛出异常
        :return: 生成的检测周期条目列表
        """
        try:
            # 获取项目报价信息
            quotation = await self.project_quotation_service.get_by_id(project_quotation_id)
            if not quotation:
                raise ServiceException(message="项目报价不存在")
            
            # 检查审批状态是否为已审核（状态为2）
            if quotation.status != "2":
                raise ServiceException(message="项目报价尚未审核通过，无法生成检测周期条目")
            
            # 获取项目报价明细，排除样品来源为"送样"的数据
            stmt = select(ProjectQuotationItem).where(
                ProjectQuotationItem.project_quotation_id == project_quotation_id,
                ProjectQuotationItem.sample_source != "送样"
            )
            result = await self.db.execute(stmt)
            quotation_items = result.scalars().all()
            if not quotation_items:
                if silent_mode:
                    return []  # 静默模式下返回空列表
                else:
                    raise ServiceException(message="项目报价明细为空或全部为送样数据")

            # 检查是否已经生成过周期条目
            existing_items = await self.detection_cycle_item_dao.get_items_by_project_quotation_id(project_quotation_id)
            if existing_items:
                if silent_mode:
                    # 静默模式下返回已存在的条目
                    return [self._convert_to_dto(item) for item in existing_items]
                else:
                    raise ServiceException(message="该项目报价已生成检测周期条目")
            
            # 为每个报价明细生成周期条目
            cycle_items_to_create = []
            
            for quotation_item in quotation_items:
                # 获取周期数，如果为空或0，则默认为1
                cycle_count = quotation_item.cycle_count if quotation_item.cycle_count and quotation_item.cycle_count > 0 else 1
                
                # 为每个周期创建条目
                for cycle_number in range(1, cycle_count + 1):
                    cycle_item = DetectionCycleItem(
                        project_quotation_id=project_quotation_id,
                        project_quotation_item_id=quotation_item.id,
                        cycle_number=cycle_number,
                        status=0,  # 未分配状态
                        create_by=create_by
                    )
                    cycle_items_to_create.append(cycle_item)
            
            # 批量创建周期条目
            created_items = await self.detection_cycle_item_dao.batch_create_detection_cycle_items(cycle_items_to_create)
            
            await self.db.commit()
            
            # 转换为DTO并返回
            return [self._convert_to_dto(item) for item in created_items]
            
        except Exception as e:
            await self.db.rollback()
            if isinstance(e, ServiceException):
                raise e
            raise ServiceException(message=f"生成检测周期条目失败: {str(e)}")
    
    async def get_cycle_items_by_project_quotation_id(self, project_quotation_id: int) -> List[DetectionCycleItemDTO]:
        """根据项目报价ID获取检测周期条目列表"""
        items = await self.detection_cycle_item_dao.get_items_by_project_quotation_id(project_quotation_id)
        return [self._convert_to_dto(item) for item in items]
    
    async def get_unassigned_cycle_items_by_project_quotation_id(self, project_quotation_id: int) -> List[DetectionCycleItemDTO]:
        """根据项目报价ID获取未分配的检测周期条目列表"""
        items = await self.detection_cycle_item_dao.get_unassigned_items_by_project_quotation_id(project_quotation_id)
        return [self._convert_to_dto(item) for item in items]
    
    async def get_cycle_item_by_id(self, item_id: int) -> Optional[DetectionCycleItemDTO]:
        """根据ID获取检测周期条目"""
        item = await self.detection_cycle_item_dao.get_detection_cycle_item_by_id(item_id)
        if item:
            return self._convert_to_dto(item)
        return None
    
    async def update_cycle_item_status(self, item_id: int, status: int, update_by: int) -> DetectionCycleItemDTO:
        """更新检测周期条目状态"""
        try:
            item = await self.detection_cycle_item_dao.get_detection_cycle_item_by_id(item_id)
            if not item:
                raise ServiceException(message="检测周期条目不存在")
            
            item.status = status
            item.update_by = update_by
            
            updated_item = await self.detection_cycle_item_dao.update_detection_cycle_item(item)
            
            await self.db.commit()
            
            return self._convert_to_dto(updated_item)
            
        except Exception as e:
            await self.db.rollback()
            if isinstance(e, ServiceException):
                raise e
            raise ServiceException(message=f"更新检测周期条目状态失败: {str(e)}")
    
    async def batch_update_cycle_item_status(self, item_ids: List[int], status: int, update_by: int) -> bool:
        """批量更新检测周期条目状态"""
        try:
            # 验证所有条目是否存在
            items = await self.detection_cycle_item_dao.get_detection_cycle_items_by_ids(item_ids)
            if len(items) != len(item_ids):
                raise ServiceException(message="部分检测周期条目不存在")
            
            # 批量更新状态
            result = await self.detection_cycle_item_dao.batch_update_status(item_ids, status, update_by)
            
            await self.db.commit()
            
            return result
            
        except Exception as e:
            await self.db.rollback()
            if isinstance(e, ServiceException):
                raise e
            raise ServiceException(message=f"批量更新检测周期条目状态失败: {str(e)}")
    
    async def page_cycle_items(self, 
                        page: int = 1, 
                        size: int = 10,
                        project_quotation_id: Optional[int] = None,
                        project_quotation_item_id: Optional[int] = None,
                        status: Optional[int] = None) -> Tuple[List[DetectionCycleItemDTO], int]:
        """分页查询检测周期条目"""
        items, total = await self.detection_cycle_item_dao.page_detection_cycle_items(
            page=page,
            size=size,
            project_quotation_id=project_quotation_id,
            project_quotation_item_id=project_quotation_item_id,
            status=status
        )
        
        item_dtos = [self._convert_to_dto(item) for item in items]
        return item_dtos, total
    
    async def delete_cycle_items_by_project_quotation_id(self, project_quotation_id: int, delete_by: int) -> bool:
        """根据项目报价ID删除所有检测周期条目"""
        try:
            # 检查是否有已分配的条目
            items = await self.detection_cycle_item_dao.get_items_by_project_quotation_id(project_quotation_id)
            assigned_items = [item for item in items if item.status != 0]
            
            if assigned_items:
                raise ServiceException(message="存在已分配的检测周期条目，无法删除")
            
            result = await self.detection_cycle_item_dao.delete_items_by_project_quotation_id(project_quotation_id)
            
            await self.db.commit()
            
            return result
            
        except Exception as e:
            await self.db.rollback()
            if isinstance(e, ServiceException):
                raise e
            raise ServiceException(message=f"删除检测周期条目失败: {str(e)}")
    
    async def regenerate_cycle_items_for_quotation(self, project_quotation_id: int, create_by: int) -> List[DetectionCycleItemDTO]:
        """重新生成项目报价的检测周期条目"""
        try:
            # 先删除现有的周期条目
            await self.delete_cycle_items_by_project_quotation_id(project_quotation_id, create_by)
            
            # 重新生成周期条目
            return await self.generate_cycle_items_for_quotation(project_quotation_id, create_by)
            
        except Exception as e:
            await self.db.rollback()
            if isinstance(e, ServiceException):
                raise e
            raise ServiceException(message=f"重新生成检测周期条目失败: {str(e)}")
    
    def _convert_to_dto(self, item: DetectionCycleItem) -> DetectionCycleItemDTO:
        """将实体转换为DTO"""
        item_dto = DetectionCycleItemDTO(
            id=item.id,
            project_quotation_id=item.project_quotation_id,
            project_quotation_item_id=item.project_quotation_item_id,
            cycle_number=item.cycle_number,
            status=item.status,
            status_label=item.status_label
        )
        
        # 设置关联信息
        if hasattr(item, 'project_quotation') and item.project_quotation:
            item_dto.project_name = item.project_quotation.project_name
        
        if hasattr(item, 'project_quotation_item') and item.project_quotation_item:
            quotation_item = item.project_quotation_item
            item_dto.detection_qualification = quotation_item.qualification_code
            item_dto.detection_classification = quotation_item.classification
            item_dto.detection_category = quotation_item.category
            item_dto.detection_parameter = quotation_item.parameter
            item_dto.detection_method = quotation_item.method
            item_dto.sample_source = quotation_item.sample_source
            item_dto.point_name = quotation_item.point_name
            item_dto.cycle_type = quotation_item.cycle_type  # 添加周期类型

        # 设置其他字段
        item_dto.assigned_to = None  # 检测周期条目本身没有assigned_to字段，设为None
        item_dto.cycle_description = None
        item_dto.planned_detection_date = None
        item_dto.actual_detection_date = None
        item_dto.detection_location = None
        item_dto.sample_requirement = None
        item_dto.remarks = None
        item_dto.create_by = item.create_by
        item_dto.create_time = item.create_time
        item_dto.update_by = item.update_by
        item_dto.update_time = item.update_time

        return item_dto