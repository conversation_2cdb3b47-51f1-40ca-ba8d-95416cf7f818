"""
合同关联报价单控制器
"""

from fastapi import APIRouter, Depends, Path
from typing import Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession

from config.get_db import get_db
from module_contract.service.contract_quotation_relation_service import ContractQuotationRelationService
from module_contract.entity.vo.contract_quotation_relation_vo import (
    ContractQuotationRelationCreateModel,
    ContractQuotationRelationUpdateModel,
    ContractQuotationRelationListModel
)
from utils.response_util import ResponseUtil
from module_admin.service.login_service import LoginService
from module_admin.entity.vo.user_vo import CurrentUserModel
from utils.log_util import logger

# 创建路由
contract_quotation_relation_controller = APIRouter(prefix="/contract/quotation-relation", tags=["合同关联报价单"])

@contract_quotation_relation_controller.get("/{contract_id}", response_model=ContractQuotationRelationListModel)
async def get_contract_quotation_relations(
    contract_id: int = Path(..., description="合同ID"),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    """
    查询合同关联的报价单列表

    Args:
        contract_id: 合同ID
        db: 数据库会话
        current_user: 当前用户

    Returns:
        Dict[str, Any]: 响应结果
    """
    try:

        service = ContractQuotationRelationService(db)
        result = await service.get_contract_quotation_relations(contract_id)
        return ResponseUtil.success(data=result.model_dump())
    except Exception as e:
        logger.error(f"查询合同关联报价单列表失败，合同ID: {contract_id}, 错误: {str(e)}")
        return ResponseUtil.error(msg=f"查询合同关联报价单列表失败: {str(e)}")


@contract_quotation_relation_controller.post("/{contract_id}", response_model=Dict[str, Any])
async def create_contract_quotation_relations(
    create_model: ContractQuotationRelationCreateModel,
    contract_id: int = Path(..., description="合同ID"),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    """
    创建合同关联报价单

    Args:
        create_model: 创建模型
        contract_id: 合同ID
        db: 数据库会话
        current_user: 当前用户

    Returns:
        Dict[str, Any]: 响应结果
    """
    try:
        service = ContractQuotationRelationService(db)
        result = await service.create_contract_quotation_relations(
            contract_id, create_model, current_user
        )
        
        if result:
            logger.info(f"创建合同关联报价单成功，合同ID: {contract_id}")
            return ResponseUtil.success(msg="创建合同关联报价单成功")
        else:
            logger.error(f"创建合同关联报价单失败，合同ID: {contract_id}")
            return ResponseUtil.error(msg="创建合同关联报价单失败")
            
    except Exception as e:
        logger.error(f"创建合同关联报价单失败，合同ID: {contract_id}, 错误: {str(e)}")
        return ResponseUtil.error(msg=f"创建合同关联报价单失败: {str(e)}")


@contract_quotation_relation_controller.put("/{contract_id}", response_model=Dict[str, Any])
async def update_contract_quotation_relations(
    update_model: ContractQuotationRelationUpdateModel,
    contract_id: int = Path(..., description="合同ID"),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    """
    更新合同关联报价单

    Args:
        update_model: 更新模型
        contract_id: 合同ID
        db: 数据库会话
        current_user: 当前用户

    Returns:
        Dict[str, Any]: 响应结果
    """
    try:
        service = ContractQuotationRelationService(db)
        result = await service.update_contract_quotation_relations(
            contract_id, update_model, current_user
        )
        
        if result:
            logger.info(f"更新合同关联报价单成功，合同ID: {contract_id}")
            return ResponseUtil.success(msg="更新合同关联报价单成功")
        else:
            logger.error(f"更新合同关联报价单失败，合同ID: {contract_id}")
            return ResponseUtil.error(msg="更新合同关联报价单失败")
            
    except Exception as e:
        logger.error(f"更新合同关联报价单失败，合同ID: {contract_id}, 错误: {str(e)}")
        return ResponseUtil.error(msg=f"更新合同关联报价单失败: {str(e)}")


# 删除接口已移除，因为前端改为全量保存模式，通过更新接口来处理删除操作
