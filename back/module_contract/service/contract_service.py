"""
合同管理服务
"""

import json
from datetime import datetime
from typing import Dict, Any, List

from sqlalchemy import select, func, and_, or_, update
from sqlalchemy.ext.asyncio import AsyncSession

from exceptions.exception import ServiceException
from module_admin.entity.do.dept_do import SysDept
from module_admin.entity.do.user_do import SysUser
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_contract.entity.do.contract_do import Contract
from module_contract.entity.vo.contract_vo import (
    AddContractModel,
    EditContractModel,
    ContractQueryModel,
)
from utils.common_util import CamelCaseUtil
from utils.page_util import PageResponseModel


class ContractService:
    """合同管理服务类"""

    def __init__(self, db: AsyncSession):
        self.db = db

    async def _get_user_names_by_ids(self, user_ids: List[int]) -> Dict[int, str]:
        """根据用户ID获取用户姓名"""
        if not user_ids:
            return {}

        query = select(SysUser.user_id, SysUser.nick_name).where(SysUser.user_id.in_(user_ids), SysUser.del_flag == "0")
        result = await self.db.execute(query)
        users = result.fetchall()

        return {user.user_id: user.nick_name for user in users}

    async def _get_id_by_nick_name(self, user_or_nick_name: str) -> int:
        """根据用户姓名获取用户ID"""
        if not user_or_nick_name:
            return 0

        query = select(SysUser.user_id, SysUser.nick_name).where(
            or_(SysUser.nick_name == user_or_nick_name, SysUser.user_name == user_or_nick_name), SysUser.del_flag == "0"
        )
        result = await self.db.execute(query)
        users = result.fetchall()
        return users[0].user_id if users else 0

    async def _get_dept_names_by_ids(self, dept_ids: List[int]) -> Dict[int, str]:
        """根据部门ID获取部门名称"""
        if not dept_ids:
            return {}

        query = select(SysDept.dept_id, SysDept.dept_name).where(SysDept.dept_id.in_(dept_ids), SysDept.del_flag == "0")
        result = await self.db.execute(query)
        depts = result.fetchall()

        return {dept.dept_id: dept.dept_name for dept in depts}

    async def get_contract_list(self, query_model: ContractQueryModel) -> PageResponseModel:
        """获取合同列表"""
        try:
            # 构建查询条件
            conditions = [Contract.del_flag == "0"]

            if query_model.contract_name:
                conditions.append(Contract.contract_name.like(f"%{query_model.contract_name}%"))
            if query_model.contract_number:
                conditions.append(Contract.contract_number.like(f"%{query_model.contract_number}%"))
            if query_model.business_type:
                conditions.append(Contract.business_type == query_model.business_type)
            if query_model.client_name:
                conditions.append(Contract.client_name.like(f"%{query_model.client_name}%"))
            if query_model.project_manager and query_model.project_service:
                project_manager_id = await self._get_id_by_nick_name(query_model.project_manager)
                project_service_id = await self._get_id_by_nick_name(query_model.project_service)
                conditions.append(
                    or_(
                        Contract.project_service_id == project_service_id,
                        func.json_contains(Contract.project_manager_ids, f"{project_manager_id}"),
                    )
                )
            elif query_model.project_manager:
                project_manager_id = await self._get_id_by_nick_name(query_model.project_manager)
                if project_manager_id:
                    conditions.append(Contract.project_manager_ids.in_(project_manager_id))
            elif query_model.project_service:
                project_service_id = await self._get_id_by_nick_name(query_model.project_service)
                if project_service_id:
                    conditions.append(Contract.project_service_id == project_service_id)
            if query_model.contract_status:
                conditions.append(Contract.contract_status == query_model.contract_status)

            # 查询总数
            count_query = select(func.count(Contract.id)).where(and_(*conditions))
            total_result = await self.db.execute(count_query)
            total = total_result.scalar()

            # 分页查询
            offset = (query_model.page_num - 1) * query_model.page_size
            query = (
                select(Contract)
                .where(and_(*conditions))
                .order_by(Contract.create_time.desc())
                .offset(offset)
                .limit(query_model.page_size)
            )
            result = await self.db.execute(query)
            contracts = result.scalars().all()

            # 获取所有用户ID和部门ID
            all_user_ids = set()
            all_dept_ids = set()
            for contract in contracts:
                if contract.project_manager_ids:
                    all_user_ids.update(contract.project_manager_ids)
                if contract.project_service_id:
                    all_user_ids.add(contract.project_service_id)
                if contract.dept_id:
                    all_dept_ids.add(contract.dept_id)

            # 获取用户姓名和部门名称映射
            user_names = await self._get_user_names_by_ids(list(all_user_ids))
            dept_names = await self._get_dept_names_by_ids(list(all_dept_ids))

            # 转换为响应模型
            contract_list = []
            for contract in contracts:
                contract_dict = {
                    "id": contract.id,
                    "contract_name": contract.contract_name,
                    "contract_number": contract.contract_number,
                    "external_contract_number": contract.external_contract_number,
                    "business_type": contract.business_type,
                    "region_province": contract.region_province,
                    "region_city": contract.region_city,
                    "client_name": contract.client_name,
                    "client_contact": contract.client_contact,
                    "acquisition_method": contract.acquisition_method,
                    "project_manager_ids": (contract.project_manager_ids if contract.project_manager_ids else []),
                    "project_manager_names": [
                        user_names.get(uid, f"用户{uid}")
                        for uid in (contract.project_manager_ids if contract.project_manager_ids else [])
                    ],
                    "project_service_id": contract.project_service_id,
                    "project_service_name": (
                        user_names.get(contract.project_service_id, f"用户{contract.project_service_id}")
                        if contract.project_service_id
                        else None
                    ),
                    "dept_id": contract.dept_id,
                    "dept_name": (
                        dept_names.get(contract.dept_id, f"部门{contract.dept_id}") if contract.dept_id else None
                    ),
                    "contract_sign_date": contract.contract_sign_date,
                    "amount_type": contract.amount_type,
                    "contract_amount": contract.contract_amount,
                    "quotation_total_amount": contract.quotation_total_amount,
                    "changed_contract_amount": contract.changed_contract_amount,
                    "outsourcing_amount": contract.outsourcing_amount,
                    "outsourcing_company": contract.outsourcing_company,
                    "completion_time": contract.completion_time,
                    "contract_status": contract.contract_status,
                    "remark": contract.remark,
                    "create_by": contract.create_by,
                    "create_time": contract.create_time,
                    "update_by": contract.update_by,
                    "update_time": contract.update_time,
                }
                contract_list.append(CamelCaseUtil.transform_result(contract_dict))

            return PageResponseModel(
                rows=contract_list, total=total, page_num=query_model.page_num, page_size=query_model.page_size
            )

        except Exception as e:
            raise ServiceException(message=f"获取合同列表失败：{str(e)}")

    async def get_contract_detail(self, contract_id: int) -> Dict[str, Any]:
        """获取合同详情"""
        try:
            # 先自动计算并更新报价单总金额
            try:
                await self.calculate_and_update_quotation_total_amount(contract_id)
            except Exception as e:
                # 如果计算失败，记录日志但不影响主流程
                print(f"自动计算报价单总金额失败: {str(e)}")

            # 重新获取更新后的合同信息
            contract = await self.get_and_check_contract(contract_id)

            # 获取用户和部门信息
            all_user_ids = set()
            if contract.project_manager_ids:
                manager_ids = contract.project_manager_ids
                all_user_ids.update(manager_ids)
            if contract.project_service_id:
                all_user_ids.add(contract.project_service_id)

            user_names = await self._get_user_names_by_ids(list(all_user_ids))
            dept_names = await self._get_dept_names_by_ids([contract.dept_id] if contract.dept_id else [])

            # 处理项目负责人信息
            manager_ids = contract.project_manager_ids if contract.project_manager_ids else []
            manager_names = [user_names.get(uid, f"用户{uid}") for uid in manager_ids]

            # 处理项目客服信息
            service_name = (
                user_names.get(contract.project_service_id, f"用户{contract.project_service_id}")
                if contract.project_service_id
                else None
            )

            contract_dict = {
                "id": contract.id,
                "contract_name": contract.contract_name,
                "contract_number": contract.contract_number,
                "external_contract_number": contract.external_contract_number,
                "business_type": contract.business_type,
                "region_province": contract.region_province,
                "region_city": contract.region_city,
                "client_name": contract.client_name,
                "client_contact": contract.client_contact,
                "acquisition_method": contract.acquisition_method,
                "project_manager_ids": manager_ids,
                "project_manager_names": manager_names,
                "project_service_id": contract.project_service_id,
                "project_service_name": service_name,
                "dept_id": contract.dept_id,
                "dept_name": dept_names.get(contract.dept_id, f"部门{contract.dept_id}") if contract.dept_id else None,
                "contract_sign_date": contract.contract_sign_date,
                "amount_type": contract.amount_type,
                "contract_amount": contract.contract_amount,
                "quotation_total_amount": contract.quotation_total_amount,
                "changed_contract_amount": contract.changed_contract_amount,
                "outsourcing_amount": contract.outsourcing_amount,
                "outsourcing_company": contract.outsourcing_company,
                "completion_time": contract.completion_time,
                "contract_status": contract.contract_status,
                "remark": contract.remark,
            }

            return CamelCaseUtil.transform_result(contract_dict)

        except ServiceException:
            raise
        except Exception as e:
            raise ServiceException(message=f"获取合同信息失败：{str(e)}")

    async def get_and_check_contract(self, contract_id):
        # 使用 selectinload 或者直接查询所有需要的字段，避免延迟加载
        query = select(Contract).where(Contract.id == contract_id)
        result = await self.db.execute(query)
        contract = result.scalar_one_or_none()
        if not contract:
            raise ServiceException(message=f"合同:{contract_id},不存在")

        # 确保所有属性都已加载，避免延迟加载问题
        await self.db.refresh(contract)
        return contract

    async def add_contract(self, contract_data: AddContractModel, current_user: CurrentUserModel) -> Dict[str, Any]:
        """新增合同"""
        try:
            # 处理项目负责人JSON数据
            contract = Contract(
                contract_name=contract_data.contract_name,
                contract_number=contract_data.contract_number,
                external_contract_number=contract_data.external_contract_number,
                business_type=contract_data.business_type,
                region_province=contract_data.region_province,
                region_city=contract_data.region_city,
                client_name=contract_data.client_name,
                client_contact=contract_data.client_contact,
                acquisition_method=contract_data.acquisition_method,
                project_manager_ids=contract_data.project_manager_ids or [],
                project_service_id=contract_data.project_service_id,
                dept_id=contract_data.dept_id,
                contract_sign_date=contract_data.contract_sign_date,
                amount_type=contract_data.amount_type,
                contract_amount=contract_data.contract_amount,
                quotation_total_amount=contract_data.quotation_total_amount,
                changed_contract_amount=contract_data.changed_contract_amount,
                outsourcing_amount=contract_data.outsourcing_amount,
                outsourcing_company=contract_data.outsourcing_company,
                completion_time=contract_data.completion_time,
                contract_status=contract_data.contract_status,
                remark=contract_data.remark,
                create_by=current_user.user.user_name,
                create_time=datetime.now(),
            )

            self.db.add(contract)
            await self.db.commit()
            await self.db.refresh(contract)

            return {"id": contract.id, "message": "合同添加成功"}

        except ServiceException:
            await self.db.rollback()
            raise
        except Exception as e:
            await self.db.rollback()
            raise ServiceException(message=f"添加合同失败：{str(e)}")

    async def update_contract(self, contract_data: EditContractModel, current_user: CurrentUserModel) -> Dict[str, Any]:
        """更新合同"""
        try:
            # 获取现有合同
            contract = await self.get_and_check_contract(contract_data.id)

            # 更新合同信息
            contract.contract_name = contract_data.contract_name
            contract.contract_number = contract_data.contract_number
            contract.external_contract_number = contract_data.external_contract_number
            contract.business_type = contract_data.business_type
            contract.region_province = contract_data.region_province
            contract.region_city = contract_data.region_city
            contract.client_name = contract_data.client_name
            contract.client_contact = contract_data.client_contact
            contract.acquisition_method = contract_data.acquisition_method
            contract.project_manager_ids = contract_data.project_manager_ids or []
            contract.project_service_id = contract_data.project_service_id
            contract.dept_id = contract_data.dept_id
            contract.contract_sign_date = contract_data.contract_sign_date
            contract.amount_type = contract_data.amount_type
            contract.contract_amount = contract_data.contract_amount
            contract.quotation_total_amount = contract_data.quotation_total_amount
            contract.changed_contract_amount = contract_data.changed_contract_amount
            contract.outsourcing_amount = contract_data.outsourcing_amount
            contract.outsourcing_company = contract_data.outsourcing_company
            contract.completion_time = contract_data.completion_time
            contract.contract_status = contract_data.contract_status
            contract.remark = contract_data.remark
            contract.update_by = current_user.user.user_name
            contract.update_time = datetime.now()

            await self.db.commit()

            return {"message": "合同更新成功"}

        except ServiceException:
            await self.db.rollback()
            raise
        except Exception as e:
            await self.db.rollback()
            raise ServiceException(message=f"更新合同失败：{str(e)}")

    async def delete_contract(self, contract_id: int, current_user: CurrentUserModel) -> Dict[str, Any]:
        """删除合同（软删除）"""
        try:
            # 获取合同
            contract = await self.get_and_check_contract(contract_id)

            # 软删除
            contract.del_flag = "1"
            contract.update_by = current_user.user.user_name
            contract.update_time = datetime.now()

            # 删除关联的报价单
            from module_contract.service.contract_quotation_relation_service import ContractQuotationRelationService
            quotation_relation_service = ContractQuotationRelationService()
            quotation_relation_service.delete_contract_quotation_relations(contract_id)

            await self.db.commit()

            return {"message": "合同删除成功"}

        except ServiceException:
            await self.db.rollback()
            raise
        except Exception as e:
            await self.db.rollback()
            raise ServiceException(message=f"删除合同失败：{str(e)}")

    async def batch_delete_contracts(self, contract_ids: List[int], current_user: CurrentUserModel) -> Dict[str, Any]:
        """批量删除合同（软删除）"""
        try:
            # 获取合同列表
            query = select(Contract).where(Contract.id.in_(contract_ids), Contract.del_flag == "0")
            result = await self.db.execute(query)
            contracts = result.scalars().all()

            if not contracts:
                raise ServiceException(message="未找到要删除的合同")

            # 批量软删除
            for contract in contracts:
                contract.del_flag = "1"
                contract.update_by = current_user.user.user_name
                contract.update_time = datetime.now()

            await self.db.commit()

            return {"message": f"成功删除{len(contracts)}个合同"}

        except ServiceException:
            await self.db.rollback()
            raise
        except Exception as e:
            await self.db.rollback()
            raise ServiceException(message=f"批量删除合同失败：{str(e)}")

    async def calculate_and_update_quotation_total_amount(self, contract_id: int) -> Dict[str, Any]:
        """
        计算并更新合同的报价单总金额

        Args:
            contract_id: 合同ID

        Returns:
            Dict[str, Any]: 更新结果
        """
        try:
            from module_contract.service.contract_quotation_relation_service import ContractQuotationRelationService
            from module_quotation.service.project_quotation_fee_calculation_service import ProjectQuotationFeeCalculationService
            from module_quotation.entity.do.project_quotation_do import ProjectQuotation
            from module_quotation.entity.do.project_quotation_total_fee_do import ProjectQuotationTotalFee
            from decimal import Decimal

            # 获取关联的报价单列表
            from module_contract.entity.do.contract_quotation_relation_do import ContractQuotationRelation

            # 直接查询关联记录
            relations_query = select(ContractQuotationRelation).where(
                ContractQuotationRelation.contract_id == contract_id
            )
            relations_result = await self.db.execute(relations_query)
            relations = relations_result.scalars().all()

            total_amount = Decimal("0")

            # 遍历每个关联的报价单，计算优惠后总金额
            for relation in relations:
                project_code = relation.project_code
                if not project_code:
                    continue

                # 根据项目编号获取项目报价ID
                quotation_query = select(ProjectQuotation.id).where(
                    ProjectQuotation.project_code == project_code,
                    ProjectQuotation.del_flag == "0"
                )
                quotation_result = await self.db.execute(quotation_query)
                quotation_id = quotation_result.scalar_one_or_none()

                if not quotation_id:
                    continue

                # 获取报价单的总费用信息
                total_fee_query = select(ProjectQuotationTotalFee).where(
                    ProjectQuotationTotalFee.project_quotation_id == quotation_id
                )
                total_fee_result = await self.db.execute(total_fee_query)
                total_fee = total_fee_result.scalar_one_or_none()

                if total_fee and total_fee.final_amount:
                    total_amount += total_fee.final_amount

            # 使用update语句直接更新合同的报价单总金额，避免对象状态问题
            update_query = update(Contract).where(Contract.id == contract_id).values(
                quotation_total_amount=total_amount,
                update_time=datetime.now()
            )
            await self.db.execute(update_query)
            await self.db.commit()

            return {
                "message": "报价单总金额计算完成",
                "quotation_total_amount": float(total_amount)
            }

        except ServiceException:
            await self.db.rollback()
            raise
        except Exception as e:
            await self.db.rollback()
            raise ServiceException(message=f"计算报价单总金额失败：{str(e)}")
