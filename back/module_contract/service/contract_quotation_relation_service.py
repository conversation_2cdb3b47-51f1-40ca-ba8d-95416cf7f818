"""
合同关联报价单服务
"""

from typing import List, Optional, Dict
from datetime import datetime
from sqlalchemy import and_, delete, select
from sqlalchemy.ext.asyncio import AsyncSession
from module_contract.entity.do.contract_quotation_relation_do import ContractQuotationRelation
from module_contract.entity.vo.contract_quotation_relation_vo import (
    ContractQuotationRelationModel,
    ContractQuotationRelationDetailModel,
    ContractQuotationRelationCreateModel,
    ContractQuotationRelationUpdateModel,
    ContractQuotationRelationListModel
)
from module_quotation.entity.do.project_quotation_do import ProjectQuotation
from module_admin.entity.vo.user_vo import CurrentUserModel
from utils.common_util import CamelCaseUtil


class ContractQuotationRelationService:
    """
    合同关联报价单服务类
    """

    def __init__(self, db: AsyncSession):
        self.db = db

    async def get_contract_quotation_relations(self, contract_id: int) -> ContractQuotationRelationListModel:
        """
        查询合同关联的报价单列表

        Args:
            contract_id: 合同ID

        Returns:
            ContractQuotationRelationListModel: 关联报价单列表
        """
        try:
            # 查询关联记录
            relations_query = select(ContractQuotationRelation).where(
                ContractQuotationRelation.contract_id == contract_id
            )
            relations_result = await self.db.execute(relations_query)
            relations = relations_result.scalars().all()

            detail_list = []
            for relation in relations:
                # 获取项目报价详情
                try:
                    project_query = select(ProjectQuotation).where(
                        ProjectQuotation.project_code == relation.project_code,
                    )
                    project_result = await self.db.execute(project_query)
                    project = project_result.scalar_one_or_none()

                    detail = ContractQuotationRelationDetailModel(
                        id=relation.id,
                        contractId=relation.contract_id,
                        projectCode=relation.project_code,
                        projectName=project.project_name if project else None,
                        customerName=project.customer_name if project else None,
                        projectManager=project.project_manager if project else None,
                        status=project.status if project else None,
                        createTime=relation.create_time
                    )
                    detail_list.append(detail)
                except Exception as e:
                    # 如果获取项目详情失败，仍然返回基本信息
                    detail = ContractQuotationRelationDetailModel(
                        id=relation.id,
                        contractId=relation.contract_id,
                        projectCode=relation.project_code,
                        projectName=None,
                        customerName=None,
                        projectManager=None,
                        status=None,
                        createTime=relation.create_time
                    )
                    detail_list.append(detail)

            return ContractQuotationRelationListModel(
                total=len(detail_list),
                rows=detail_list
            )

        except Exception as e:
            raise e

    async def create_contract_quotation_relations(
        self,
        contract_id: int,
        create_model: ContractQuotationRelationCreateModel,
        current_user: CurrentUserModel
    ) -> bool:
        """
        创建合同关联报价单

        Args:
            contract_id: 合同ID
            create_model: 创建模型
            current_user: 当前用户

        Returns:
            bool: 创建结果
        """
        try:
            # 先删除现有关联
            await self.db.execute(
                delete(ContractQuotationRelation).where(
                    ContractQuotationRelation.contract_id == contract_id
                )
            )

            # 创建新的关联记录
            for project_code in create_model.project_codes:
                relation = ContractQuotationRelation(
                    contract_id=contract_id,
                    project_code=project_code,
                    create_by=current_user.user.user_name,
                    create_time=datetime.now()
                )
                self.db.add(relation)

            await self.db.commit()
            return True

        except Exception as e:
            await self.db.rollback()
            raise e

    async def update_contract_quotation_relations(
        self,
        contract_id: int,
        update_model: ContractQuotationRelationUpdateModel,
        current_user: CurrentUserModel
    ) -> bool:
        """
        更新合同关联报价单

        Args:
            contract_id: 合同ID
            update_model: 更新模型
            current_user: 当前用户

        Returns:
            bool: 更新结果
        """
        result = await self.create_contract_quotation_relations(contract_id, update_model, current_user)

        # 更新成功后，触发报价单总金额重新计算
        if result:
            try:
                from module_contract.service.contract_service import ContractService
                contract_service = ContractService(self.db)
                await contract_service.calculate_and_update_quotation_total_amount(contract_id)
            except Exception as e:
                print(f"触发报价单总金额计算失败: {str(e)}")

        return result

    # 删除方法已移除，因为前端改为全量保存模式，通过更新方法来处理删除操作
